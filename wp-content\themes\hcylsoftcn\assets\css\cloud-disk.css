/* 企业网盘页面样式 */

/* CSS变量定义 */
:root {
  --cloud-disk-primary: #1e40af;
  --cloud-disk-hover: #1d4ed8;
  --cloud-disk-text: #333;
  --cloud-disk-secondary: #666;
  --cloud-disk-bg: #f8f9fa;
  --cloud-disk-light-bg: #ffffff;
  --cloud-disk-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  --cloud-disk-hover-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --cloud-disk-border-radius: 12px;
  --cloud-disk-transition: all 0.3s ease;
}

/* 页面容器 */
.cloud-disk {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.6;
  color: var(--cloud-disk-text);
}

/* 通用容器样式 */
.cloud-disk-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Banner样式 */
.cloud-disk #hero {
  position: relative;
  overflow: hidden;
  padding: 0px;
}

.cloud-disk .banner-container {
  width: 100%;
  height: auto;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cloud-disk .banner-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 通用section样式 */
.cloud-disk section {
  padding: 80px 0;
}

/* 特定section样式 */
.cloud-disk #scenarios {
  background-color: #f8f9fa;
  padding: 80px 0;
}

.cloud-disk #space-management {
  padding: 80px 0;
}

.cloud-disk #permission-management {
  background-color: #f8f9fa;
  padding: 80px 0;
}

.cloud-disk #file-sharing {
  padding: 80px 0;
}

.cloud-disk #mobile-support {
  background-color: #f8f9fa;
  padding: 80px 0;
}

.cloud-disk #security-storage {
  padding: 80px 0;
}

.cloud-disk #backup-recovery {
  background-color: #f8f9fa;
  padding: 80px 0;
}

.cloud-disk .section-header {
  text-align: center;
  margin-bottom: 60px;
}

.cloud-disk .section-header h2 {
  font-size: 32px;
  color: var(--cloud-disk-text);
  margin-bottom: 16px;
  font-weight: 700;
  position: relative;
  display: inline-block;
}

.cloud-disk .section-header h2::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--cloud-disk-primary), var(--cloud-disk-hover));
  border-radius: 2px;
}

/* 产品介绍样式 */
.cloud-disk .intro-text {
  text-align: left;
  max-width: 100%;
}

.cloud-disk .intro-text p {
  max-width: 100%;
  margin: 0 0 30px;
  line-height: 1.8;
  color: var(--cloud-disk-text);
  font-size: 16px;
}

.cloud-disk .market-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.cloud-disk .market-image img {
  width: 100%;
  height: auto;
  border-radius: var(--cloud-disk-border-radius);
  box-shadow: var(--cloud-disk-shadow);
}

/* 解决方案网格 */
.cloud-disk .scenarios-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}

.cloud-disk .scenario-card {
  background: var(--cloud-disk-light-bg);
  border-radius: var(--cloud-disk-border-radius);
  text-align: center;
  transition: var(--cloud-disk-transition);
  box-shadow: var(--cloud-disk-shadow);
  border: 1px solid rgba(30, 64, 175, 0.1);
}

.cloud-disk .scenario-card .scenario-content {
  padding: 20px 30px;
}

.cloud-disk .scenario-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--cloud-disk-hover-shadow);
}

.cloud-disk .scenario-icon {
  margin: 0 auto 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cloud-disk .scenario-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.cloud-disk .scenario-content h3 {
  font-size: 20px;
  color: var(--cloud-disk-text);
  margin-bottom: 12px;
  font-weight: 600;
}

.cloud-disk .scenario-content p {
  font-size: 14px;
  line-height: 1.6;
  color: var(--cloud-disk-secondary);
  margin: 0;
}

/* 空间管理模块样式 */
.cloud-disk .space-management {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-top: 40px;
}

.cloud-disk .space-card {
  background: var(--cloud-disk-light-bg);
  border-radius: var(--cloud-disk-border-radius);
  padding: 30px;
  text-align: center;
  transition: var(--cloud-disk-transition);
  box-shadow: var(--cloud-disk-shadow);
  border: 1px solid rgba(30, 64, 175, 0.1);
}

.cloud-disk .space-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--cloud-disk-hover-shadow);
}

.cloud-disk .space-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(30, 64, 175, 0.1);
  border-radius: 20px;
}

.cloud-disk .space-icon i {
  font-size: 32px;
  color: var(--cloud-disk-primary);
}

.cloud-disk .space-content h3 {
  font-size: 20px;
  color: var(--cloud-disk-text);
  margin-bottom: 16px;
  font-weight: 600;
}

.cloud-disk .space-content p {
  font-size: 16px;
  line-height: 1.6;
  color: var(--cloud-disk-secondary);
  margin: 0;
}

/* 权限管理网格 */
.cloud-disk .permission-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

.cloud-disk .permission-card {
  background: var(--cloud-disk-light-bg);
  border-radius: var(--cloud-disk-border-radius);
  overflow: hidden;
  transition: var(--cloud-disk-transition);
  box-shadow: var(--cloud-disk-shadow);
  border: 1px solid rgba(30, 64, 175, 0.1);
}

.cloud-disk .permission-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--cloud-disk-hover-shadow);
}

.cloud-disk .permission-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.cloud-disk .permission-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cloud-disk .permission-content {
  padding: 24px;
}

.cloud-disk .permission-content h3 {
  font-size: 20px;
  color: var(--cloud-disk-text);
  margin-bottom: 12px;
  font-weight: 600;
}

.cloud-disk .permission-content p {
  font-size: 14px;
  line-height: 1.6;
  color: var(--cloud-disk-secondary);
  margin: 0;
}

/* 文件共享网格 */
.cloud-disk .sharing-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-top: 40px;
}

.cloud-disk .sharing-card {
  background: var(--cloud-disk-light-bg);
  border-radius: var(--cloud-disk-border-radius);
  padding: 40px 30px;
  text-align: center;
  transition: var(--cloud-disk-transition);
  box-shadow: var(--cloud-disk-shadow);
  border: 1px solid rgba(30, 64, 175, 0.1);
  position: relative;
  overflow: hidden;
}

.cloud-disk .sharing-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  transition: var(--cloud-disk-transition);
  z-index: 1;
}

.cloud-disk .sharing-card-1::before {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
}

.cloud-disk .sharing-card-2::before {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
}

.cloud-disk .sharing-card-3::before {
  background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(251, 146, 60, 0.1) 100%);
}

.cloud-disk .sharing-card:hover::before {
  opacity: 1;
}

.cloud-disk .sharing-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.cloud-disk .sharing-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  position: relative;
  z-index: 2;
  transition: var(--cloud-disk-transition);
}

.cloud-disk .sharing-card-1 .sharing-icon {
  background: linear-gradient(135deg, #3b82f6 0%, #9333ea 100%);
}

.cloud-disk .sharing-card-2 .sharing-icon {
  background: linear-gradient(135deg, #10b981 0%, #3b82f6 100%);
}

.cloud-disk .sharing-card-3 .sharing-icon {
  background: linear-gradient(135deg, #f56565 0%, #fb923c 100%);
}

.cloud-disk .sharing-icon i {
  font-size: 32px;
  color: white;
}

.cloud-disk .sharing-card:hover .sharing-icon {
  transform: scale(1.1);
}

.cloud-disk .sharing-content {
  position: relative;
  z-index: 2;
}

.cloud-disk .sharing-content h3 {
  font-size: 20px;
  color: var(--cloud-disk-text);
  margin-bottom: 16px;
  font-weight: 600;
}

.cloud-disk .sharing-content p {
  font-size: 16px;
  line-height: 1.6;
  color: var(--cloud-disk-secondary);
  margin: 0;
}

/* 移动端支持样式 */
.cloud-disk .mobile-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.cloud-disk .mobile-feature {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 30px;
}

.cloud-disk .feature-number {
  width: 40px;
  height: 40px;
  background: var(--cloud-disk-primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 18px;
  flex-shrink: 0;
}

.cloud-disk .feature-detail h3 {
  font-size: 20px;
  color: var(--cloud-disk-text);
  margin-bottom: 8px;
  font-weight: 600;
}

.cloud-disk .feature-detail p {
  font-size: 16px;
  line-height: 1.6;
  color: var(--cloud-disk-secondary);
  margin: 0;
}

.cloud-disk .mobile-image img {
  width: 100%;
  height: auto;
  border-radius: var(--cloud-disk-border-radius);
}

/* 安全与存储网格 */
.cloud-disk .security-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

.cloud-disk .security-card {
  background: var(--cloud-disk-light-bg);
  border-radius: var(--cloud-disk-border-radius);
  overflow: hidden;
  transition: var(--cloud-disk-transition);
  box-shadow: var(--cloud-disk-shadow);
  border: 1px solid rgba(30, 64, 175, 0.1);
}

.cloud-disk .security-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--cloud-disk-hover-shadow);
}

.cloud-disk .security-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.cloud-disk .security-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cloud-disk .security-content {
  padding: 24px;
}

.cloud-disk .security-content h3 {
  font-size: 20px;
  color: var(--cloud-disk-text);
  margin-bottom: 12px;
  font-weight: 600;
}

.cloud-disk .security-content p {
  font-size: 14px;
  line-height: 1.6;
  color: var(--cloud-disk-secondary);
  margin: 0;
}

/* 备份恢复样式 */
.cloud-disk .notification-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.cloud-disk .notification-image img {
  width: 100%;
  height: auto;
  border-radius: var(--cloud-disk-border-radius);
  box-shadow: var(--cloud-disk-shadow);
}

.cloud-disk .notification-item {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 30px;
}

.cloud-disk .item-icon {
  width: 40px;
  height: 40px;
  background: var(--cloud-disk-primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 18px;
  flex-shrink: 0;
}

.cloud-disk .item-content h3 {
  font-size: 20px;
  color: var(--cloud-disk-text);
  margin-bottom: 8px;
  font-weight: 600;
}

.cloud-disk .item-content p {
  font-size: 16px;
  line-height: 1.6;
  color: var(--cloud-disk-secondary);
  margin: 0;
}

/* 用户案例样式 */
.cloud-disk .case-studies {
  background: var(--cloud-disk-bg);
}

.cloud-disk .section-title {
  font-size: 32px;
  color: var(--cloud-disk-text);
  text-align: center;
  margin-bottom: 60px;
  font-weight: 700;
  position: relative;
  display: inline-block;
  width: 100%;
}

.cloud-disk .section-title::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--cloud-disk-primary), var(--cloud-disk-hover));
  border-radius: 2px;
}

.cloud-disk .case-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

.cloud-disk .case-card {
  background: var(--cloud-disk-light-bg);
  border-radius: var(--cloud-disk-border-radius);
  overflow: hidden;
  transition: var(--cloud-disk-transition);
  box-shadow: var(--cloud-disk-shadow);
  border: 1px solid rgba(30, 64, 175, 0.1);
}

.cloud-disk .case-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--cloud-disk-hover-shadow);
}

.cloud-disk .case-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.cloud-disk .case-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cloud-disk .case-content {
  padding: 24px;
}

.cloud-disk .case-content h3 {
  font-size: 20px;
  color: var(--cloud-disk-text);
  margin-bottom: 12px;
  font-weight: 600;
}

.cloud-disk .case-content p {
  font-size: 14px;
  line-height: 1.6;
  color: var(--cloud-disk-secondary);
  margin: 0;
}

/* 产品优势模块样式 */
.cloud-disk #advantages {
  background-color: var(--cloud-disk-bg);
  padding: 80px 0;
}

.cloud-disk .advantages-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
  margin-top: 40px;
}

.cloud-disk .advantage-card {
  background: var(--cloud-disk-light-bg);
  border-radius: var(--cloud-disk-border-radius);
  overflow: hidden;
  transition: var(--cloud-disk-transition);
  box-shadow: var(--cloud-disk-shadow);
  border: 1px solid rgba(30, 64, 175, 0.1);
}

.cloud-disk .advantage-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--cloud-disk-hover-shadow);
}

.cloud-disk .advantage-image {
  width: 100%;
  aspect-ratio: 16/9;
  overflow: hidden;
}

.cloud-disk .advantage-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cloud-disk .advantage-content {
  padding: 24px;
  text-align: center;
}

.cloud-disk .advantage-content h3 {
  font-size: 20px;
  color: var(--cloud-disk-text);
  margin-bottom: 12px;
  font-weight: 600;
}

.cloud-disk .advantage-content p {
  font-size: 14px;
  line-height: 1.6;
  color: var(--cloud-disk-secondary);
  margin: 0;
}

/* 核心功能模块样式 */
.cloud-disk .feature-item {
  display: flex;
  align-items: center;
  gap: 60px;
  margin-bottom: 80px;
  background: var(--cloud-disk-light-bg);
  border-radius: var(--cloud-disk-border-radius);
  padding: 40px;
  transition: var(--cloud-disk-transition);
  box-shadow: var(--cloud-disk-shadow);
}

.cloud-disk .feature-item.reverse {
  flex-direction: row-reverse;
}

.cloud-disk .feature-text {
  flex: 0 0 50%;
}

.cloud-disk .feature-image {
  flex: 0 0 45%;
  max-width: 533px;
  height: 300px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cloud-disk .feature-image img {
  width: 100%;
  height: 100%;
  border-radius: var(--cloud-disk-border-radius);
  object-fit: cover;
  box-shadow: var(--cloud-disk-shadow);
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .cloud-disk .scenarios-grid,
  .cloud-disk .permission-grid,
  .cloud-disk .sharing-grid,
  .cloud-disk .security-grid,
  .cloud-disk .case-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }

  .cloud-disk .space-management {
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }

  .cloud-disk .advantages-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }
}

@media (max-width: 992px) {
  .cloud-disk section {
    padding: 60px 0;
  }

  .cloud-disk .section-header {
    margin-bottom: 40px;
  }

  .cloud-disk .section-header h2,
  .cloud-disk .section-title {
    font-size: 28px;
  }

  .cloud-disk .market-content,
  .cloud-disk .mobile-content,
  .cloud-disk .notification-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .cloud-disk .scenarios-grid,
  .cloud-disk .permission-grid,
  .cloud-disk .sharing-grid,
  .cloud-disk .security-grid,
  .cloud-disk .case-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .cloud-disk .space-management {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .cloud-disk .feature-item {
    flex-direction: column;
    gap: 30px;
    padding: 30px;
  }

  .cloud-disk .feature-item.reverse {
    flex-direction: column;
  }

  .cloud-disk .feature-text,
  .cloud-disk .feature-image {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .cloud-disk .cloud-disk-container {
    padding: 0 15px;
  }

  .cloud-disk #hero {
    display: none;
  }

  .cloud-disk section {
    padding: 50px 0;
  }

  .cloud-disk .section-header h2,
  .cloud-disk .section-title {
    font-size: 24px;
  }

  .cloud-disk .scenarios-grid,
  .cloud-disk .permission-grid,
  .cloud-disk .sharing-grid,
  .cloud-disk .security-grid,
  .cloud-disk .case-grid,
  .cloud-disk .space-management {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .cloud-disk .mobile-feature,
  .cloud-disk .notification-item {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .cloud-disk .feature-number,
  .cloud-disk .item-icon {
    align-self: center;
  }
}

@media (max-width: 576px) {
  .cloud-disk .cloud-disk-container {
    padding: 0 10px;
  }

  .cloud-disk .banner-container {
    height: 250px;
  }

  .cloud-disk section {
    padding: 40px 0;
  }

  .cloud-disk .section-header {
    margin-bottom: 30px;
  }

  .cloud-disk .section-header h2,
  .cloud-disk .section-title {
    font-size: 22px;
  }

  .cloud-disk .space-card,
  .cloud-disk .scenario-card,
  .cloud-disk .sharing-card {
    padding: 24px;
  }

  .cloud-disk .sharing-icon {
    width: 60px;
    height: 60px;
    margin-bottom: 20px;
  }

  .cloud-disk .sharing-icon i {
    font-size: 24px;
  }

  .cloud-disk .permission-content,
  .cloud-disk .security-content,
  .cloud-disk .case-content {
    padding: 20px;
  }

  .cloud-disk .advantages-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .cloud-disk .advantage-content {
    padding: 20px;
  }
}
