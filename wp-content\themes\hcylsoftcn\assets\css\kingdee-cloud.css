/* 金蝶云容器解决方案页面专用样式 */

/* 页面特定变量 */
:root {
  --kingdee-primary: #0052d9;
  --kingdee-hover: #0066ff;
  --kingdee-text: #2c3e50;
  --kingdee-secondary: #7f8c8d;
  --kingdee-bg: #f8f9fa;
  --kingdee-light-bg: #ffffff;
  --kingdee-shadow: 0 8px 25px rgba(0, 82, 217, 0.1);
  --kingdee-card-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  --kingdee-hover-shadow: 0 8px 30px rgba(0, 82, 217, 0.15);
  --kingdee-border-radius: 12px;
  --kingdee-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 页面容器 */
.kingdee-cloud {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
  line-height: 1.6;
  color: var(--kingdee-text);
}

/* 通用容器样式 */
.kingdee-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Banner区域 */
.kingdee-cloud .hero-section {
  position: relative;
  height: 650px;
  background: linear-gradient(135deg, var(--kingdee-primary) 0%, var(--kingdee-hover) 100%);
  color: #ffffff;
  overflow: hidden;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.kingdee-cloud .hero-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 82, 217, 0.1);
  z-index: 1;
}

.kingdee-cloud .banner-container {
  position: relative;
  height: 100%;
  width: 100%;
  z-index: 2;
}

.kingdee-cloud .banner-item {
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.kingdee-cloud .banner-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.kingdee-cloud .hero-content {
  text-align: center;
  z-index: 3;
  position: relative;
}

.kingdee-cloud .hero-content h1 {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.02em;
}

.kingdee-cloud .hero-content p {
  font-size: 1.25rem;
  opacity: 0.95;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* 方案概述 */
.kingdee-cloud .overview-section {
  padding: 80px 0;
  background: var(--kingdee-light-bg);
  position: relative;
}

.kingdee-cloud .overview-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--kingdee-primary), transparent);
}

.kingdee-cloud .overview-content {
  display: flex;
  gap: 60px;
  align-items: center;
  position: relative;
}

.kingdee-cloud .overview-text {
  flex: 1;
  padding-right: 40px;
}

.kingdee-cloud .overview-text p {
  color: var(--kingdee-text);
  line-height: 1.8;
  margin-bottom: 24px;
  font-size: 17px;
  font-weight: 400;
  text-align: justify;
}

.kingdee-cloud .overview-text p:last-child {
  margin-bottom: 0;
}

.kingdee-cloud .overview-text p:first-child {
  position: relative;
  padding-left: 20px;
}

.kingdee-cloud .overview-text p:first-child::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, var(--kingdee-primary), var(--kingdee-hover));
  border-radius: 2px;
}

.kingdee-cloud .overview-image {
  flex: 1;
  position: relative;
}

.kingdee-cloud .overview-image::before {
  content: "";
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: linear-gradient(135deg, var(--kingdee-primary), var(--kingdee-hover));
  border-radius: var(--kingdee-border-radius);
  opacity: 0.1;
  z-index: 1;
}

.kingdee-cloud .overview-image img {
  width: 100%;
  border-radius: var(--kingdee-border-radius);
  box-shadow: var(--kingdee-shadow);
  position: relative;
  z-index: 2;
  transition: var(--kingdee-transition);
}

.kingdee-cloud .overview-image:hover img {
  transform: translateY(-5px);
  box-shadow: var(--kingdee-hover-shadow);
}

/* 自研产品 */
.kingdee-cloud .products-section {
  background: #f8f9fa;
  padding: 80px 0;
}

.kingdee-cloud .section-header {
  text-align: center;
  margin-bottom: 100px;
  position: relative;
}

.kingdee-cloud .section-header h2 {
  font-size: 32px;
  color: var(--kingdee-text);
  margin-bottom: 16px;
  font-weight: 700;
  position: relative;
  display: inline-block;
  letter-spacing: -0.02em;
}

.kingdee-cloud .section-header h2::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, var(--kingdee-primary), var(--kingdee-hover));
  border-radius: 2px;
}

.kingdee-cloud .section-header p {
  color: var(--kingdee-secondary);
  font-size: 18px;
  max-width: 650px;
  margin: 0 auto;
  line-height: 1.6;
}

.kingdee-cloud .products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
}

.kingdee-cloud .product-card {
  background: var(--kingdee-light-bg);
  border-radius: var(--kingdee-border-radius);
  box-shadow: var(--kingdee-card-shadow);
  transition: var(--kingdee-transition);
  overflow: hidden;
  position: relative;
  border: 1px solid rgba(0, 82, 217, 0.1);
}

.kingdee-cloud .product-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--kingdee-primary), var(--kingdee-hover));
  transform: scaleX(0);
  transition: var(--kingdee-transition);
}

.kingdee-cloud .product-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--kingdee-hover-shadow);
}

.kingdee-cloud .product-card:hover::before {
  transform: scaleX(1);
}

.kingdee-cloud .product-image {
  width: 100%;
  height: 220px;
  overflow: hidden;
  position: relative;
}

.kingdee-cloud .product-image::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 82, 217, 0.1), rgba(0, 102, 255, 0.1));
  opacity: 0;
  transition: var(--kingdee-transition);
}

.kingdee-cloud .product-card:hover .product-image::after {
  opacity: 1;
}

.kingdee-cloud .product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--kingdee-transition);
}

.kingdee-cloud .product-card:hover .product-image img {
  transform: scale(1.05);
}

.kingdee-cloud .product-content {
  padding: 30px 25px;
}

.kingdee-cloud .product-content h3 {
  font-size: 24px;
  color: var(--kingdee-text);
  margin-bottom: 18px;
  font-weight: 700;
  line-height: 1.3;
}

.kingdee-cloud .product-content p {
  color: var(--kingdee-secondary);
  line-height: 1.7;
  margin-bottom: 0;
  font-size: 15px;
  text-align: justify;
}

/* 定制化开发产品 */
.kingdee-cloud .custom-products-section {
  padding: 80px 0;
  background: var(--kingdee-light-bg);
}

.kingdee-cloud .custom-products-grid {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.kingdee-cloud .custom-product-card {
  background: var(--kingdee-light-bg);
  border-radius: var(--kingdee-border-radius);
  box-shadow: var(--kingdee-card-shadow);
  transition: var(--kingdee-transition);
  overflow: hidden;
  display: grid;
  grid-template-columns: 350px 1fr;
  position: relative;
  border: 1px solid rgba(0, 82, 217, 0.08);
}

.kingdee-cloud .custom-product-card::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, var(--kingdee-primary), var(--kingdee-hover));
  transform: scaleY(0);
  transition: var(--kingdee-transition);
}

.kingdee-cloud .custom-product-card:hover {
  transform: translateX(10px);
  box-shadow: var(--kingdee-hover-shadow);
}

.kingdee-cloud .custom-product-card:hover::before {
  transform: scaleY(1);
}

.kingdee-cloud .custom-product-card .product-image {
  height: 250px;
  overflow: hidden;
  position: relative;
}

.kingdee-cloud .custom-product-card .product-image::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 82, 217, 0.1), rgba(0, 102, 255, 0.1));
  opacity: 0;
  transition: var(--kingdee-transition);
}

.kingdee-cloud .custom-product-card:hover .product-image::after {
  opacity: 1;
}

.kingdee-cloud .custom-product-card .product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--kingdee-transition);
}

.kingdee-cloud .custom-product-card:hover .product-image img {
  transform: scale(1.05);
}

.kingdee-cloud .custom-product-card .product-content {
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.kingdee-cloud .custom-product-card .product-content h3 {
  font-size: 24px;
  color: var(--kingdee-text);
  margin-bottom: 20px;
  font-weight: 700;
  line-height: 1.3;
}

.kingdee-cloud .custom-product-card .product-content p {
  color: var(--kingdee-secondary);
  line-height: 1.8;
  margin-bottom: 0;
  font-size: 16px;
  text-align: justify;
}

/* 二次开发产品 */
.kingdee-cloud .secondary-dev-section {
  background: var(--kingdee-bg);
  padding: 80px 0;
}

.kingdee-cloud .secondary-dev-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 40px;
}

.kingdee-cloud .secondary-dev-card {
  background: var(--kingdee-light-bg);
  border-radius: var(--kingdee-border-radius);
  box-shadow: var(--kingdee-card-shadow);
  transition: var(--kingdee-transition);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  border: 1px solid rgba(0, 82, 217, 0.1);
}

.kingdee-cloud .secondary-dev-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--kingdee-primary), var(--kingdee-hover));
  transform: scaleX(0);
  transition: var(--kingdee-transition);
}

.kingdee-cloud .secondary-dev-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--kingdee-hover-shadow);
}

.kingdee-cloud .secondary-dev-card:hover::before {
  transform: scaleX(1);
}

.kingdee-cloud .dev-image {
  width: 100%;
  height: 180px;
  overflow: hidden;
  position: relative;
}

.kingdee-cloud .dev-image::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 82, 217, 0.1), rgba(0, 102, 255, 0.1));
  opacity: 0;
  transition: var(--kingdee-transition);
}

.kingdee-cloud .secondary-dev-card:hover .dev-image::after {
  opacity: 1;
}

.kingdee-cloud .dev-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--kingdee-transition);
}

.kingdee-cloud .secondary-dev-card:hover .dev-image img {
  transform: scale(1.05);
}

.kingdee-cloud .dev-content {
  padding: 25px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.kingdee-cloud .dev-content h3 {
  font-size: 24px;
  color: var(--kingdee-text);
  margin-bottom: 15px;
  font-weight: 700;
  line-height: 1.3;
}

.kingdee-cloud .dev-content p {
  color: var(--kingdee-secondary);
  line-height: 1.7;
  margin-bottom: 0;
  flex: 1;
  font-size: 14px;
  text-align: justify;
}

/* 集成项目 */
.kingdee-cloud .integration-section {
  padding: 80px 0;
  background: var(--kingdee-light-bg);
}

.kingdee-cloud .integration-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 40px;
}

.kingdee-cloud .integration-card {
  background: var(--kingdee-light-bg);
  border-radius: var(--kingdee-border-radius);
  box-shadow: var(--kingdee-card-shadow);
  transition: var(--kingdee-transition);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  border: 1px solid rgba(0, 82, 217, 0.1);
}

.kingdee-cloud .integration-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--kingdee-primary), var(--kingdee-hover));
  transform: scaleX(0);
  transition: var(--kingdee-transition);
}

.kingdee-cloud .integration-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--kingdee-hover-shadow);
}

.kingdee-cloud .integration-card:hover::before {
  transform: scaleX(1);
}

.kingdee-cloud .integration-image {
  width: 100%;
  height: 180px;
  overflow: hidden;
  position: relative;
}

.kingdee-cloud .integration-image::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 82, 217, 0.1), rgba(0, 102, 255, 0.1));
  opacity: 0;
  transition: var(--kingdee-transition);
}

.kingdee-cloud .integration-card:hover .integration-image::after {
  opacity: 1;
}

.kingdee-cloud .integration-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--kingdee-transition);
}

.kingdee-cloud .integration-card:hover .integration-image img {
  transform: scale(1.05);
}

.kingdee-cloud .integration-content {
  padding: 25px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.kingdee-cloud .integration-content h3 {
  font-size: 24px;
  color: var(--kingdee-text);
  margin-bottom: 15px;
  font-weight: 700;
  line-height: 1.3;
}

.kingdee-cloud .integration-content p {
  color: var(--kingdee-secondary);
  line-height: 1.7;
  margin-bottom: 0;
  flex: 1;
  font-size: 14px;
  text-align: justify;
}

/* 服务客户 */
.kingdee-cloud .clients-section {
  background: var(--kingdee-bg);
  padding: 80px 0;
  color: var(--kingdee-text);
  position: relative;
}

.kingdee-cloud .clients-section .section-header.light h2 {
  color: var(--kingdee-text);
  position: relative;
  display: inline-block;
}

.kingdee-cloud .clients-showcase {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
  padding: 0 20px;
  position: relative;
}

.kingdee-cloud .clients-showcase::before {
  content: "";
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  background: linear-gradient(135deg, var(--kingdee-primary), var(--kingdee-hover));
  border-radius: var(--kingdee-border-radius);
  opacity: 0.05;
  z-index: 1;
}

.kingdee-cloud .clients-showcase img {
  width: 100%;
  height: auto;
  border-radius: var(--kingdee-border-radius);
  box-shadow: var(--kingdee-shadow);
  position: relative;
  z-index: 2;
  transition: var(--kingdee-transition);
}

.kingdee-cloud .clients-showcase:hover img {
  transform: translateY(-5px);
  box-shadow: var(--kingdee-hover-shadow);
}

/* 响应式调整 */
@media (max-width: 992px) {
  .kingdee-cloud .secondary-dev-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 25px;
  }

  .kingdee-cloud .integration-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 25px;
  }
}

@media (max-width: 768px) {
  .kingdee-cloud .hero-section {
    display: none;
  }

  .kingdee-cloud .overview-content {
    flex-direction: column;
    gap: 30px;
  }

  .kingdee-cloud .overview-text {
    padding-right: 0;
  }

  .kingdee-cloud .products-grid {
    grid-template-columns: 1fr;
  }

  .kingdee-cloud .custom-product-card {
    grid-template-columns: 1fr;
  }

  .kingdee-cloud .custom-product-card .product-image {
    height: 200px;
  }

  .kingdee-cloud .secondary-dev-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .kingdee-cloud .integration-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .kingdee-cloud .clients-showcase {
    padding: 0 20px;
  }

  .kingdee-cloud .section-header h2 {
    font-size: 28px;
  }
}

@media (max-width: 480px) {
  .kingdee-cloud .hero-section {
    height: 400px;
  }

  .kingdee-cloud .secondary-dev-grid {
    grid-template-columns: 1fr;
  }

  .kingdee-cloud .integration-grid {
    grid-template-columns: 1fr;
  }

  .kingdee-cloud .section-header h2 {
    font-size: 24px;
  }

  .kingdee-cloud .overview-section,
  .kingdee-cloud .products-section,
  .kingdee-cloud .custom-products-section,
  .kingdee-cloud .secondary-dev-section,
  .kingdee-cloud .integration-section,
  .kingdee-cloud .clients-section {
    padding: 80px 0;
  }
}

/* 平滑滚动和动画效果 */
html {
  scroll-behavior: smooth;
}

/* 为所有卡片添加进入动画 */
.kingdee-cloud .product-card,
.kingdee-cloud .custom-product-card,
.kingdee-cloud .secondary-dev-card,
.kingdee-cloud .integration-card {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.6s ease forwards;
}

.kingdee-cloud .product-card:nth-child(1) {
  animation-delay: 0.1s;
}
.kingdee-cloud .product-card:nth-child(2) {
  animation-delay: 0.2s;
}
.kingdee-cloud .product-card:nth-child(3) {
  animation-delay: 0.3s;
}
.kingdee-cloud .product-card:nth-child(4) {
  animation-delay: 0.4s;
}
.kingdee-cloud .product-card:nth-child(5) {
  animation-delay: 0.5s;
}

.kingdee-cloud .custom-product-card:nth-child(1) {
  animation-delay: 0.1s;
}
.kingdee-cloud .custom-product-card:nth-child(2) {
  animation-delay: 0.3s;
}
.kingdee-cloud .custom-product-card:nth-child(3) {
  animation-delay: 0.5s;
}
.kingdee-cloud .custom-product-card:nth-child(4) {
  animation-delay: 0.7s;
}
.kingdee-cloud .custom-product-card:nth-child(5) {
  animation-delay: 0.9s;
}

.kingdee-cloud .secondary-dev-card:nth-child(1) {
  animation-delay: 0.1s;
}
.kingdee-cloud .secondary-dev-card:nth-child(2) {
  animation-delay: 0.2s;
}
.kingdee-cloud .secondary-dev-card:nth-child(3) {
  animation-delay: 0.3s;
}
.kingdee-cloud .secondary-dev-card:nth-child(4) {
  animation-delay: 0.4s;
}

.kingdee-cloud .integration-card:nth-child(1) {
  animation-delay: 0.1s;
}
.kingdee-cloud .integration-card:nth-child(2) {
  animation-delay: 0.2s;
}
.kingdee-cloud .integration-card:nth-child(3) {
  animation-delay: 0.3s;
}
.kingdee-cloud .integration-card:nth-child(4) {
  animation-delay: 0.4s;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 标题动画 */
.kingdee-cloud .section-header h2 {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s ease forwards;
}

.kingdee-cloud .section-header p {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s ease 0.2s forwards;
}

/* 添加微妙的背景纹理 */
.kingdee-cloud .products-section,
.kingdee-cloud .secondary-dev-section,
.kingdee-cloud .clients-section {
  position: relative;
}

.kingdee-cloud .products-section::after,
.kingdee-cloud .secondary-dev-section::after,
.kingdee-cloud .clients-section::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 20% 50%, rgba(0, 82, 217, 0.03) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(0, 102, 255, 0.03) 0%, transparent 50%), radial-gradient(circle at 40% 80%, rgba(0, 82, 217, 0.02) 0%, transparent 50%);
  pointer-events: none;
}
