/*首页 - 合并版本*/
/* 防止横向滚动条 */
html,
body {
  overflow-x: hidden;
  max-width: 100%;
}

/* 桌面端样式 */
.swiper-container-banner {
  overflow: hidden;
  position: relative;
  width: 100%;
}

.swiper-container-banner img {
  width: 100%;
}

.swiper-container-banner .swiper-pagination-bullet {
  background: rgb(255, 255, 255, 0.5) !important;
}

.swiper-container-banner .swiper-pagination-bullet-active {
  background: #fff !important;
}

.profile-title {
  font-size: 26px;
  width: 50%;
  margin: 0 auto;
  text-align: center;
}

.profile-title-e {
  color: #d6d6d6;
  line-height: 45px;
  padding-top: 60px;
}

.profile-title-c {
  color: #222;
  margin-top: 10px;
}

.profile-line {
  width: 42px;
  height: 4px;
  background: linear-gradient(90deg, #4f7fe8, #97b5f7);
  margin: 0 auto;
  margin-top: 20px;
}

.profile-company {
  padding-bottom: 60px;
}

.company-profile {
  max-width: 1210px;
  margin: 0 auto;
  overflow: hidden;
  margin-top: 40px;
  padding: 0 20px;
}

.company-profile-l {
  width: 50%;
  float: left;
}

.company-profile-r {
  width: 50%;
  float: right;
}

.company-profile-r img {
  width: 100%;
}

.company-profile-title {
  font-size: 22px;
  color: #333333;
}

.company-profile-title span {
  color: #4f7fe8;
}

.company-profile-content {
  line-height: 28px;
  padding-right: 45px;
  color: #555;
  text-align: justify;
  font-size: 15px;
  margin-top: 20px;
  position: relative;
  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
  padding: 25px 30px 25px 20px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(79, 127, 232, 0.08);
  border-left: 4px solid #4f7fe8;
  transition: all 0.3s ease;
}

.company-profile-content:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(79, 127, 232, 0.15);
}

.company-profile-content::before {
  content: '"';
  position: absolute;
  top: -5px;
  left: 10px;
  font-size: 60px;
  color: rgba(79, 127, 232, 0.1);
  font-family: Georgia, serif;
  line-height: 1;
}

.company-profile-content::after {
  content: "";
  position: absolute;
  bottom: 0;
  right: 0;
  width: 40px;
  height: 40px;
  background: linear-gradient(45deg, transparent 50%, rgba(79, 127, 232, 0.05) 50%);
  border-radius: 0 0 12px 0;
}

.more {
  font-size: 16px;
  color: #4f7fe8;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  display: inline-block;
}

.more:visited {
  color: #4f7fe8;
}

.more:hover {
  color: #3a5fd6;
  text-decoration: none;
}

.profile-solution {
  background: url("../img/index/solution-bg.png");
  background-size: 100% 100%;
  padding-bottom: 60px;
}

.profile-eco-solution {
  background: url("../img/index/solution-bg.png");
  background-size: 100% 100%;
  padding-bottom: 60px;
}

.solution-box {
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 获取解决方案 */
.solution-content-box {
  text-align: center;
  padding: 15px 18px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  min-height: 200px;
}

.solution-get-btn {
  background: transparent;
  color: #4f7fe8;
  padding: 15px 30px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  margin: 20px auto 0;
  box-shadow: none;
  border: 2px solid #4f7fe8;
  min-width: 200px;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  display: inline-block;
  text-decoration: none;
}

.solution-get-btn:hover {
  background: #4f7fe8;
  color: white;
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 25px rgba(79, 127, 232, 0.3);
  border: 2px solid #4f7fe8;
  text-decoration: none;
}

.solution-get-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.solution-get-btn:hover::before {
  left: 100%;
}

.solution-get-btn::after {
  content: "📞";
  font-size: 18px;
  margin-left: 10px;
  transition: all 0.3s ease;
}

.solution-get-btn:hover::after {
  transform: rotate(15deg) scale(1.2);
}

/* 解决方案轮播容器样式 */
.swiper-container-solution {
  overflow: hidden;
  padding: 40px 0;
  max-width: 1210px;
  margin: 0px 10px;
}

.swiper-container-eco-solution {
  overflow: hidden;
  padding: 40px 0;
  max-width: 1210px;
  margin: 0px 10px;
}

/* 生态解决方案居中显示 */
.hcyl-eco-solution {
  justify-content: center !important;
}

.swiper-container-eco-solution .swiper-wrapper {
  justify-content: center !important;
}

/* 强制修复生态解决方案轮播项宽度 */
.swiper-container-eco-solution .swiper-wrapper {
  display: flex !important;
}

/* 生态解决方案轮播项基础样式 */
.swiper-container-eco-solution .swiper-slide-eco-solution {
  display: flex;
  justify-content: center;
  align-items: stretch;
  width: auto !important;
  flex-shrink: 0;
}

/* 产品中心样式 */
.profile-product {
  background: url(../img/index/product-bg.png);
  background-size: 100% 100%;
  position: relative;
  padding-bottom: 60px;
}

/* 产品中心轮播样式 */
.swiper-container-product {
  overflow: hidden;
  padding: 20px 0;
  max-width: 1210px;
  margin: 0px 10px;
  /* margin: 0 auto; */
}

.product-item {
  background: #fff;
  padding: 25px;
  margin: 0 auto;
  display: block;
  text-decoration: none;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  min-height: 330px;
}

.product-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  text-decoration: none;
}

.product-img {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: #4f7fe8;
  margin: 0 auto 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-img img {
  max-width: 60px;
  max-height: 60px;
}

.product-title {
  color: #333;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  margin: 20px 0 15px;
}

.product-content {
  color: #888;
  text-align: justify;
  font-size: 14px;
  line-height: 24px;
}

.swiper-slide-product {
  display: flex;
  justify-content: center;
  align-items: stretch;
}

/* 产品中心导航按钮 */
.swiper-button-next-product,
.swiper-button-prev-product {
  position: static !important;
  top: 50% !important;
  width: 44px !important;
  height: 44px !important;
  min-width: 44px !important;
  min-height: 44px !important;
  z-index: 10 !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: #fff !important;
  background: rgba(0, 0, 0, 0.3) !important;
  border-radius: 50% !important;
  border: none !important;
  margin: 0 !important;
  padding: 0 !important;
}
.swiper-button-next-product::after,
.swiper-button-prev-product::after {
  font-size: 25px !important;
  text-transform: none !important;
  letter-spacing: 0;
  font-variant: initial;
  font-weight: bold;
  display: block !important;
  line-height: 1 !important;
}

.swiper-button-prev-product {
  left: -35px !important;
}
.swiper-button-prev-product:after {
  content: "‹";
}

.swiper-button-next-product {
  right: 10px !important;
}
.swiper-button-next-product:after {
  content: "›";
}

/* 解决方案导航按钮 */
.swiper-button-next-solution,
.swiper-button-prev-solution {
  position: static !important;
  top: 50% !important;
  width: 44px !important;
  height: 44px !important;
  min-width: 44px !important;
  min-height: 44px !important;
  z-index: 10 !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: #fff !important;
  background: rgba(0, 0, 0, 0.3) !important;
  border-radius: 50% !important;
  border: none !important;
  margin: 0 !important;
  padding: 0 !important;
}
.swiper-button-next-solution::after,
.swiper-button-prev-solution::after {
  font-size: 25px !important;
  text-transform: none !important;
  letter-spacing: 0;
  font-variant: initial;
  font-weight: bold;
  display: block !important;
  line-height: 1 !important;
}

.swiper-button-prev-solution {
  left: -35px !important;
}
.swiper-button-prev-solution:after {
  content: "‹";
}

.swiper-button-next-solution {
  right: 10px !important;
}
.swiper-button-next-solution:after {
  content: "›";
}

/* 生态解决方案导航按钮 */
.swiper-button-next-eco-solution,
.swiper-button-prev-eco-solution {
  position: static !important;
  top: 50% !important;
  width: 44px !important;
  height: 44px !important;
  min-width: 44px !important;
  min-height: 44px !important;
  z-index: 10 !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: #fff !important;
  background: rgba(0, 0, 0, 0.3) !important;
  border-radius: 50% !important;
  border: none !important;
  margin: 0 !important;
  padding: 0 !important;
}
.swiper-button-next-eco-solution::after,
.swiper-button-prev-eco-solution::after {
  font-size: 25px !important;
  text-transform: none !important;
  letter-spacing: 0;
  font-variant: initial;
  font-weight: bold;
  display: block !important;
  line-height: 1 !important;
}

.swiper-button-prev-eco-solution {
  left: -35px !important;
}
.swiper-button-prev-eco-solution:after {
  content: "‹";
}

.swiper-button-next-eco-solution {
  right: 10px !important;
}
.swiper-button-next-eco-solution:after {
  content: "›";
}

.swiper-slide-solution {
  display: flex;
  justify-content: center;
  align-items: stretch;
}

.swiper-slide-eco-solution {
  display: flex;
  justify-content: center;
  align-items: stretch;
  width: auto !important;
  flex-shrink: 0;
}

/* PC端生态解决方案轮播项宽度 - 与解决方案保持一致 */
@media (min-width: 1024px) {
  .swiper-container-eco-solution .swiper-slide-eco-solution {
    width: calc(33.333% - 20px) !important;
    flex: 0 0 calc(33.333% - 20px) !important;
    max-width: calc(33.333% - 20px) !important;
    min-width: auto !important;
  }
}

/* 平板端生态解决方案轮播项宽度 - 与解决方案保持一致 */
@media (min-width: 768px) and (max-width: 1023px) {
  .swiper-container-eco-solution .swiper-slide-eco-solution {
    width: calc(50% - 15px) !important;
    flex: 0 0 calc(50% - 15px) !important;
    max-width: calc(50% - 15px) !important;
    min-width: auto !important;
  }
}

/* 移动端生态解决方案轮播项宽度 - 与解决方案保持一致 */
@media (max-width: 767px) {
  .swiper-container-eco-solution .swiper-slide-eco-solution {
    width: 100% !important;
    flex: 0 0 100% !important;
    max-width: 100% !important;
    min-width: auto !important;
    margin: 0 !important;
  }

  /* 确保移动端生态解决方案容器正确显示 */
  .swiper-container-eco-solution {
    margin: 0 20px;
    max-width: calc(100% - 40px);
  }

  /* 移动端生态解决方案内容项样式调整 */
  .swiper-container-eco-solution .solution-item {
    margin: 0 auto;
    max-width: 100%;
  }

  /* 移动端生态解决方案wrapper样式重置 */
  .swiper-container-eco-solution .swiper-wrapper {
    justify-content: flex-start !important;
    transform: translate3d(0px, 0, 0) !important;
    width: 100% !important;
  }

  /* 强制移动端生态解决方案只显示一个slide */
  .swiper-container-eco-solution .swiper-slide-eco-solution {
    width: 100% !important;
    flex: 0 0 100% !important;
    max-width: 100% !important;
    min-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* 超强制移动端生态解决方案样式 */
  .hcyl-eco-solution .swiper-container-eco-solution .swiper-wrapper .swiper-slide-eco-solution {
    width: 100% !important;
    flex: 0 0 100% !important;
    max-width: 100% !important;
    min-width: 100% !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  /* 确保wrapper不会显示多个slides */
  .hcyl-eco-solution .swiper-container-eco-solution .swiper-wrapper {
    width: 100% !important;
    transform: translate3d(0px, 0, 0) !important;
  }

  /* 移动端hcyl-solution和hcyl-eco-solution容器样式重置 */
  .hcyl-solution,
  .hcyl-eco-solution {
    justify-content: flex-start !important;
    padding: 0 20px !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }
}

/* 解决方案项目样式 */
.solution-item {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
  display: block;
  text-decoration: none;
  height: 100%;
  position: relative;
}

.solution-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(79, 127, 232, 0.15);
  text-decoration: none;
}

.solution-img {
  position: relative;
  overflow: hidden;
  height: 200px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.solution-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.solution-item:hover .solution-img img {
  transform: scale(1.05);
}

.solution-title {
  font-size: 18px !important;
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 10px;
  margin-top: 10px;
  line-height: 1.4;
  transition: color 0.3s ease;
  text-align: center;
}

.solution-item:hover .solution-title {
  color: #4f7fe8;
}

.solution-content {
  color: #7f8c8d;
  line-height: 1.6;
  font-size: 14px;
  text-align: left;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
  overflow: hidden;
  margin-bottom: 15px;
  min-height: 80px;
  flex: 1;
}

.hcyl-solution .swiper-button-prev-solution:after,
.hcyl-solution .swiper-button-next-solution:after {
  color: #ffffff !important;
}

.hcyl-eco-solution .swiper-button-prev-eco-solution:after,
.hcyl-eco-solution .swiper-button-next-eco-solution:after {
  color: #ffffff !important;
}

/* PC端强制显示导航按钮 */
@media (min-width: 768px) {
  .swiper-button-next-product,
  .swiper-button-prev-product,
  .swiper-button-next-solution,
  .swiper-button-prev-solution,
  .swiper-button-next-eco-solution,
  .swiper-button-prev-eco-solution {
    display: flex !important;
    width: 44px !important;
    height: 44px !important;
    min-width: 44px !important;
    min-height: 44px !important;
    background: rgba(0, 0, 0, 0.3) !important;
    border-radius: 50% !important;
    border: none !important;
  }
}

/* 平板端样式 (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
  .company-profile {
    width: 90%;
    max-width: 900px;
  }

  .company-profile-content {
    line-height: 26px;
    padding-right: 25px;
    font-size: 14px;
    margin-top: 18px;
    padding: 20px 25px 20px 18px;
    border-radius: 10px;
    box-shadow: 0 3px 15px rgba(79, 127, 232, 0.08);
    border-left: 3px solid #4f7fe8;
  }

  .company-profile-content:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 25px rgba(79, 127, 232, 0.12);
  }

  .company-profile-content::before {
    top: -3px;
    left: 8px;
    font-size: 45px;
  }

  .company-profile-content::after {
    width: 30px;
    height: 30px;
    border-radius: 0 0 10px 0;
  }

  .more {
    font-size: 15px;
    margin-top: 25px;
  }

  .solution-get-btn {
    font-size: 15px;
    padding: 12px 25px;
    min-width: 180px;
  }

  .solution-img {
    height: 160px;
  }

  .solution-title {
    font-size: 16px !important;
  }

  .solution-content {
    font-size: 13px;
    -webkit-line-clamp: 4;
    margin-bottom: 15px;
  }

  /* 产品中心响应式样式 */
  .product-item {
    min-height: 280px;
    padding: 20px;
  }

  .product-img {
    width: 80px;
    height: 80px;
  }

  .product-img img {
    max-width: 50px;
    max-height: 50px;
  }

  .product-title {
    font-size: 16px;
  }

  .product-content {
    font-size: 13px;
    line-height: 22px;
  }
}

/* 移动端样式 (最大767px) */
@media (max-width: 767px) {
  * {
    box-sizing: border-box;
  }

  .swiper-container-banner {
    padding-top: 0;
  }

  .profile-title {
    width: 50%;
    margin: 0 auto;
    text-align: center;
  }

  .profile-title-e {
    font-size: 0.1rem;
    color: #d6d6d6;
    line-height: 16px;
    padding-top: 18px;
  }

  .profile-title-c {
    color: #222;
    margin-top: 10px;
    font-size: 0.17rem;
  }

  .profile-line {
    width: 0.15rem;
    height: 1px;
    margin-top: 10px;
  }

  .company-profile-l {
    width: 100%;
    padding: 0 0.13rem;
  }

  .company-profile {
    margin-top: 0;
    width: 100%;
  }

  .company-profile-title {
    margin-top: 20px;
  }

  .company-profile-r {
    width: 2.79rem;
    margin: 0 auto;
    float: inherit;
  }

  .company-profile-content {
    padding-right: 0px;
    margin-top: 0.15rem;
    font-size: 0.14rem;
    line-height: 0.22rem;
    background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
    padding: 0.2rem 0.15rem;
    border-radius: 0.08rem;
    box-shadow: 0 0.02rem 0.15rem rgba(79, 127, 232, 0.08);
    border-left: 0.02rem solid #4f7fe8;
    transition: all 0.3s ease;
  }

  .more,
  .company-img {
    display: none;
  }

  .company-profile-content::before {
    content: '"';
    position: absolute;
    top: -0.02rem;
    left: 0.05rem;
    font-size: 0.4rem;
    color: rgba(79, 127, 232, 0.1);
    font-family: Georgia, serif;
    line-height: 1;
  }

  .company-profile-content::after {
    content: "";
    position: absolute;
    bottom: 0;
    right: 0;
    width: 0.25rem;
    height: 0.25rem;
    background: linear-gradient(45deg, transparent 50%, rgba(79, 127, 232, 0.05) 50%);
    border-radius: 0 0 0.08rem 0;
  }

  .profile-company {
    padding-bottom: 0.3rem;
  }

  .profile-solution {
    padding-bottom: 0.2rem;
  }

  .solution-get-btn {
    font-size: 14px;
    padding: 12px 20px;
    border-radius: 20px;
    min-width: 160px;
    margin: 15px auto 0;
  }

  .solution-get-btn::after {
    content: "📞";
    font-size: 16px;
    margin-left: 8px;
  }

  .swiper-container-solution {
    padding: 20px 0;
    width: 100% !important;
    margin: 0 !important;
  }

  .swiper-container-eco-solution {
    padding: 20px 0;
    width: 100% !important;
    margin: 0 !important;
  }

  .hcyl-eco-solution {
    padding: 0 20px !important;
    width: 100% !important;
  }

  .solution-img {
    height: 140px;
  }

  .solution-title {
    font-size: 0.16rem !important;
    margin-bottom: 0.1rem;
  }

  .solution-content {
    font-size: 0.12rem;
    line-height: 0.18rem;
    -webkit-line-clamp: 4;
    margin-bottom: 0.15rem;
  }

  /* 产品中心在移动端也显示导航按钮 */
  .swiper-button-next-product,
  .swiper-button-prev-product {
    display: flex !important;
    width: 40px !important;
    height: 40px !important;
    min-width: 40px !important;
    min-height: 40px !important;
    background: rgba(0, 0, 0, 0.5) !important;
    border-radius: 50% !important;
    border: none !important;
    position: static !important;
    color: #fff !important;
    align-items: center !important;
    justify-content: center !important;
  }

  /* 移动端产品中心导航按钮的箭头 */
  .swiper-button-next-product::after,
  .swiper-button-prev-product::after {
    font-size: 20px !important;
    color: #fff !important;
    display: block !important;
    line-height: 1 !important;
  }

  /* 生态解决方案在移动端也显示导航按钮 */
  .swiper-button-next-eco-solution,
  .swiper-button-prev-eco-solution {
    display: flex !important;
    width: 40px !important;
    height: 40px !important;
    min-width: 40px !important;
    min-height: 40px !important;
    background: rgba(0, 0, 0, 0.5) !important;
    border-radius: 50% !important;
    border: none !important;
    position: static !important;
    color: #fff !important;
    align-items: center !important;
    justify-content: center !important;
  }

  /* 移动端生态解决方案导航按钮的箭头 */
  .swiper-button-next-eco-solution::after,
  .swiper-button-prev-eco-solution::after {
    font-size: 20px !important;
    color: #fff !important;
    display: block !important;
    line-height: 1 !important;
  }

  /* 解决方案在移动端也显示导航按钮 */
  .swiper-button-next-solution,
  .swiper-button-prev-solution {
    display: flex !important;
    width: 40px !important;
    height: 40px !important;
    min-width: 40px !important;
    min-height: 40px !important;
    background: rgba(0, 0, 0, 0.5) !important;
    border-radius: 50% !important;
    border: none !important;
    position: static !important;
    color: #fff !important;
    align-items: center !important;
    justify-content: center !important;
  }

  /* 移动端解决方案导航按钮的箭头 */
  .swiper-button-next-solution::after,
  .swiper-button-prev-solution::after {
    font-size: 20px !important;
    color: #fff !important;
    display: block !important;
    line-height: 1 !important;
  }

  /* 产品中心移动端样式 */
  .swiper-container-product {
    padding: 0.2rem 0;
    width: 80% !important;
  }

  .product-item {
    min-height: 2.5rem;
    padding: 0.15rem;
    margin: 0 0.05rem;
  }

  .product-img {
    width: 0.6rem;
    height: 0.6rem;
    margin: 0 auto 0.15rem;
  }

  .product-img img {
    max-width: 0.35rem;
    max-height: 0.35rem;
  }

  .product-title {
    font-size: 0.16rem;
    margin: 0.15rem 0 0.1rem;
  }

  .product-content {
    font-size: 0.12rem;
    line-height: 0.18rem;
  }
}

@media (max-width: 768px) {
  .product-item {
    /* width: 220px; */
    width: 280px;
    min-height: 300px;
  }
}
