.about-bg-img {
  position: relative;
}

.product-box-bg {
  background: #f7f9fc;
}

.wrapper-pad {
  /* padding-top: 90px; */
}

.about-text {
  width: 210px;
  margin: 0 auto;
  position: absolute;
  top: 30%;
  left: 50%;
  margin-left: -105px;
}

.about-c {
  font-size: 46px;
  font-weight: bold;
  line-height: 81px;
  color: #fff;
  letter-spacing: 5px;
}

.about-e {
  font-size: 20px;
  line-height: 24px;
  color: #fff;
  text-align: center;
}

.about-tabs {
  position: absolute;
  bottom: 0px;
  left: 50%;
  margin-left: -605px;
  width: 1210px;
}

.about-tabs li a {
  background: #fff;
  display: inline-block;
  line-height: 58px;
  padding-left: 85px;
  padding-right: 85px;
  color: #333;
  font-size: 18px;
}

.selected {
  background: #4f7fe8 !important;
  color: #fff !important;
}

.about-content-main {
  display: none;
}

.dis-block {
  display: block;
}

.dis-none {
  display: none;
}

.mag-t0 {
  margin-top: 0 !important;
}

/*车辆管理系统*/
.product-box {
  padding-top: 60px;
  padding-bottom: 60px;
  overflow: hidden;
}

.pro-box-1 {
  background: url("../img/product/car/product-cl.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.pro-name {
  overflow: hidden;
  width: 1210px;
  padding: 10px 0px;
  margin: 0 auto;
  margin-top: 30px;
}

.product-title {
  text-align: center;
  font-size: 32px;
  color: #333;
  line-height: 32px;
}

.product-intro {
  font-size: 16px;
  color: #999;
  text-align: center;
  padding-top: 20px;
  padding-bottom: 30px;
}

.product-line {
  width: 56px;
  height: 2px;
  background: #4f7fe8;
  margin: 0 auto;
}

.pro-content {
  padding-top: 135px;
  padding-left: 60px;
  box-sizing: border-box;
}

.pro-content p {
  font-size: 16px;
  color: #333;
  line-height: 34px;
  text-align: justify;
}

.pro-box-2 {
  background: #f7f9fc;
}

.pro-intro {
  font-size: 24px;
  color: #333;
  padding-top: 10px;
  padding-bottom: 10px;
  line-height: 32px;
}

.pro-line {
  width: 42px;
  height: 4px;
  background: linear-gradient(90deg, #4f7fe8, #97b5f7);
}

.pro-num {
  margin-top: 50px;
}

.pro-num-r {
  padding-left: 80px;
}

.pro-car-img .pro-detail {
  padding-left: 80px;
  margin-top: 20px;
}

.pro-car-box .pro-detail {
  padding-right: 50px;
  margin-top: 20px;
}

.pro-detail p {
  line-height: 32px;
  font-size: 14px;
  color: #666;
  text-align: justify;
}

.pro-detail li {
  line-height: 32px;
  font-size: 14px;
  color: #666;
}

.pro-detail li span {
  margin-left: 10px;
}

.pro-detail-item {
  margin-top: 20px;
  display: -webkit-flex;
  /* Safari */
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pro-detail-item span {
  font-size: 16px;
  color: #333;
  margin-left: 0px !important;
}

.pro-detail-item img {
  vertical-align: middle;
  margin-top: -5px;
}

.pro-pad {
  margin-top: 0;
}

.pro-box-pc {
  padding-top: 10px;
  padding-bottom: 10px;
}

.pro-pc {
  margin-top: 50px;
}

.pro-pc-t30 {
  margin-top: 30px;
}

.pro-circle img {
  width: 10px;
  height: 10px;
}

.pro-7-1 {
  width: 16px;
}

.pro-7-2 {
  width: 17px;
}

.pro-7-3 {
  width: 17px;
}

.pro-7-4 {
  width: 22px;
}

/*印章管理系统*/
.seal-content {
  padding-top: 100px;
}

.seal-point-box {
  padding: 17px;
  background: #fff;
  position: relative;
  border: 1px solid #e5ebf4;
  min-height: 183px;
  margin-left: 11px;
  margin-right: 11px;
  margin-bottom: 22px;
}

.seal-point-img {
  width: 95px;
  height: 95px;
}

.seal-point-name {
  font-size: 18px;
  color: #333;
  line-height: 32px;
  padding-left: 15px;
}

.seal-point-content {
  position: relative;
  padding-left: 27px;
  line-height: 26px;
  font-size: 14px;
  color: #999;
}

.seal-point-content::before {
  display: block;
  content: "";
  position: absolute;
  width: 4px;
  height: 4px;
  background: #4f7fe8;
  border-radius: 50%;
  left: 15px;
  top: 11px;
}

.seal-point-order {
  width: 0;
  height: 0;
  border-top: 65px solid #f5f9fe;
  border-left: 68px solid transparent;
  position: absolute;
  top: 0;
  right: 0;
}

.seal-point-num {
  color: #4f7fe8;
  position: absolute;
  top: 10px;
  right: 10px;
}

.seal-resolve-name {
  padding-left: 0;
}

.seal-resolve-content {
  padding-left: 12px;
}

.seal-resolve-content::before {
  left: 0px;
  top: 10px;
}

.seal-solve-box {
  margin-bottom: 30px;
  margin-top: 20px;
}

.solve-img {
  display: block;
  margin: 0 auto;
}

.seal-pc {
  margin-top: 70px;
}

.seal-box .pro-num-r {
  padding-left: 30px;
  margin-top: 0;
}

.seal-box .pro-car-img .pro-detail {
  padding-left: 35px;
}

.seal-box .pro-car-box .pro-detail {
  padding-right: 35px;
}

.seal-box .pro-num {
  margin-top: 0;
}

.seal-pc-1 {
  margin-top: 145px;
}

.pro-value {
  background: url("../img/product/seal/seal-value-bg.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.chanpin-youshi {
  text-align: center;
  margin-top: 20px;
  position: relative;
}

.chanpin-youshi .text {
  position: absolute;
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #666666;
  width: 240px;
}

.chanpin-youshi .text1 {
  top: 0;
  left: 150px;
}

.chanpin-youshi .text2 {
  top: 120px;
  left: 65px;
}

.chanpin-youshi .text3 {
  top: 270px;
  left: 60px;
}

.chanpin-youshi .text4 {
  top: 400px;
  left: 140px;
}

.chanpin-youshi .text5 {
  right: 115px;
  top: 50px;
}

.chanpin-youshi .text6 {
  right: 60px;
  top: 200px;
}

.chanpin-youshi .text7 {
  right: 110px;
  top: 390px;
}

.seal-value-box {
  width: 18%;
  box-shadow: 0px 0px 8px 0px rgba(212, 212, 212, 0.43);
  border-radius: 10px;
  background: url("../img/product/seal/pro-value-bg.png");
  background-repeat: no-repeat;
  float: left;
  /* padding: 30px 20px; */
  box-sizing: border-box;
  /* height: 186px; */
  margin-left: 10px;
  margin-right: 10px;
  margin-bottom: 15px;
}

.seal-value {
  /*display: flex;
    justify-content: space-between;
    align-items: center;*/
}

.pro-value-img {
  margin: 0 auto;
  display: block;
  margin-top: 25px;
  height: 43px;
}

.pro-value-content {
  padding: 10px 20px;
  margin-top: 22px;
  height: 65px;
  text-align: center;
  line-height: 30px;
  font-size: 15px;
  background: #fff;
}

.seal-blank {
  position: relative;
}

.seal-blank img {
  /*display:block;*/
  margin: 0 auto;
}

.seal-role {
  position: absolute;
}

.seal-role1 {
  right: 160px;
  top: 115px;
  max-width: 220px;
  text-align: left;
}

.seal-role2 {
  left: 38px;
  bottom: 85px;
  max-width: 195px;
  text-align: right;
}

.seal-role3 {
  right: 0px;
  bottom: 115px;
  max-width: 220px;
  text-align: left;
}

.role-name {
  font-size: 22px;
  color: #333;
  line-height: 32px;
}

.role-content {
  font-size: 15px;
  color: #999;
  line-height: 24px;
  margin-top: 20px;
}

.seal-value-center {
  position: relative;
  height: 1.03rem;
}

.seal-value-cbox {
  position: absolute;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
}

.tiaozhan-slide-item {
  text-align: center;
  position: relative;
}

.tiaozhan-slide-item img {
}

.tiaozhan-slide-item .tedian {
  width: 150px;
  height: 30px;
  line-height: 30px;
  background: #4f7fe8;
  border-radius: 2px;
  color: #fff;
  text-align: center;
  position: absolute;
  bottom: -7px;
  left: 0;
  right: 0;
  margin: auto;
  margin-bottom: 0px;
}

.tiaozhan-slide-item-text {
  width: 200px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  margin: 20px auto;
  color: #333333;
  text-align: center;
}

.chanpin-jiazhi {
  position: relative;
}

.metting-manage-container {
  margin-top: 30px;
}

.metting-manage-container .metting-manage-item {
  background: #ffffff;
  height: 400px;
  overflow: hidden;
}

.metting-manage-item img {
  float: left;
}

.metting-manage-item .text {
  padding: 60px;
  padding-left: 460px;
}

.swiper-container-huiyiguanli {
  margin-top: 30px;
  position: relative;
}

.swiper-container-tiaozhan {
  position: relative;
}

.swiper-container-tiaozhan .swiper-button-next,
.swiper-container-rtl .swiper-button-prev {
  right: 0px;
  top: 100px;
}

.swiper-container-tiaozhan .swiper-button-prev,
.swiper-container-rtl .swiper-button-next {
  left: 0px;
  top: 100px;
}

.metting-manage-item .text .state span:nth-child(1) {
  font-size: 24px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}

.metting-manage-item .text .state span:nth-child(2) {
  font-size: 18px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #4f7fe8;
  line-height: 16px;
  opacity: 0.3;
  margin-left: 15px;
}

.chanpin-jiazhi .text-container {
  position: absolute;
  width: 510px;
  height: 500px;
  background: #000000;
  opacity: 0.5;
  top: 0;
  right: 0;
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #ffffff;
  text-align: center;
  padding: 100px;
  box-sizing: border-box;
  line-height: 32px;
}

.youshi-container {
  display: flex;
  justify-content: space-around;
}

.youshi-container .youshi-item {
  text-align: center;
}

.youshi-container .youshi-item .bt-line {
  width: 20px;
  height: 3px;
  background: #ffbc4d;
  margin: 0 auto;
}

.youshi-container .youshi-item .number {
  font-size: 22px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}

.youshi-container .youshi-item .number2 {
  margin-top: 20px;
}

.youshi-container .youshi-item .text {
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  width: 180px;
  margin: 20px auto;
  color: #999999;
}

.hydb-block {
  width: 100%;
  margin-bottom: 15px;
  margin-top: 195px;
}

.duban-item1 .active {
  border-left: 3px solid #4f7fe8;
  box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.09);
}

/* .duban-item2 .active {
    border-left: 3px solid #4F7FE8;
    box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.09);
  } */
.dubanguanli-img-pane {
  display: none;
}

.duban-discribe {
  background: #ffffff;
  border: 1px solid #f5f5f5;
  padding: 25px;
  line-height: 32px;
  font-size: 14px;
  color: #666;
  height: 180px;
  cursor: pointer;
  text-align: justify;
  box-sizing: border-box;
}

.duban-discribe .duban-discribe-title {
  color: #333333;
  font-size: 16px;
}

.error {
  color: red;
}
.none {
  display: none;
}
input {
  width: 370px;
  height: 46px;
  background: #f8fafc;
  border-radius: 4px;
  border: 1px solid #e7eaf2;
  font-size: 16px;
  box-sizing: border-box;
  padding: 0 17px;
  -webkit-padding: 0 17px;
  -moz-padding: 0 17px;
}
.form-bottom {
  width: 100%;
  height: 155px;
  /*background: url('../img/build/code-bac.png');*/
  background-size: 100% 100%;
}
.main {
  width: 100%;
  height: 100vh;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  background: rgba(12, 24, 39, 0.1);
  z-index: 88;
}
.main-center {
  width: 600px;
  height: 450px;
  background: #ffffff;
  border-radius: 6px;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
}
.right-fix-box2 {
  position: fixed;
  right: 0;
  bottom: 105px;
  z-index: 300;
}
.right-fix-box2 .div {
  width: 80px;
  height: 80px;
  background: #ffffff;
  box-shadow: 0px 4px 10px 0px rgba(124, 162, 199, 0.16);
  border-radius: 4px;
  margin: 15px 10px;
  position: relative;
  cursor: pointer;
  text-align: center;
  box-sizing: border-box;
  padding-top: 18px;
}

.business-center-tab {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.business-center-bottom {
  width: 100%;
  height: 548px;
  background: #fff;
  border-radius: 10px;
  border-bottom: 20px solid #277ef0;
}
.color1 {
  font-size: 32px;
  margin: 15px 0;
  font-weight: 600;
  color: #101419;
}

.color2 {
  font-size: 16px;
  color: #222c4b;
  line-height: 30px;
}
.color3 {
  color: #fff !important;
}

.business-center-tab-center {
  width: 23.5%;
  height: 260px;
  background: #ffffff;
  border-radius: 10px;
  box-sizing: border-box;
  padding: 37px;
  cursor: pointer;
  position: relative;
}
.triangle-l {
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 10px solid #277ef0;
  position: absolute;

  right: -10px;
  top: 0;
  bottom: 0;
  margin: auto;
}
.triangle1 {
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-top: 20px solid #277ef0;
  position: absolute;
  left: 0;
  right: 0;
  bottom: -18px;
  margin: auto;
}
.triangle2 {
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-top: 20px solid #277ef0;
  position: absolute;
  left: 0;
  right: 0;
  bottom: -18px;
  margin: auto;
}
.triangle3 {
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-top: 20px solid #277ef0;
  position: absolute;
  left: 0;
  right: 0;
  bottom: -18px;
  margin: auto;
}
.triangle4 {
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-top: 20px solid #277ef0;
  position: absolute;
  left: 0;
  right: 0;
  bottom: -18px;
  margin: auto;
}

.business-center-tab-center-active {
  background: #277ef0 !important;
}

.business-center-msg {
  width: 100%;
  display: flex;
  justify-content: center;
  position: relative;
}

.business-center-msg-img-left {
  width: 50%;
  height: 186px;
  position: absolute;
  top: -186px;
  left: 0;
  right: 0;
  margin: auto;
  z-index: 8;
}

.business-center-msg-img-right {
  width: 50%;
  height: 186px;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin: auto;
  z-index: 8;
}

.business-center-msg-left {
  width: 51%;
  height: 344px;
  position: relative;
}

.business-center-msg-right {
  width: 49%;
  height: 344px;
  box-sizing: border-box;
  padding-top: 18px;
}

.business-center-msg-layout {
  width: 100%;
  display: flex;
  padding-left: 96px;
  font-family: Alibaba PuHuiTi 3;
  font-weight: normal;
  font-size: 32px;
  color: #101419;
  align-items: flex-end;
  flex-wrap: wrap;
}

.business-center-msg-layout-img {
  width: 68px;
  height: 52px;
  margin-right: 22px;
}

.business-center-msg-left-img {
  width: 100%;
  height: 100%;
}

.business-center-msg-left-img-h {
  width: 89%;
  height: 86%;
}

.business-center-msg-left-img-x {
  width: 159px;
  height: 300px;
  position: absolute;
  right: 0;
  bottom: 0;
}

.business-center {
  width: 100%;
  height: 614px;
  box-sizing: border-box;
  padding: 59px 16% 0 16%;
  background: #f5faff;
}

.swiper-container-banner {
  overflow: hidden;
  position: relative;
  width: 100%;
  height: 337px;
}

.swiper-pagination {
  width: 100%;
  text-align: left !important;
}

.swiper-pagination-bullet {
  width: 20px !important;
  height: 6px !important;
  border-radius: 3px !important;
  background: #e2e8f8 !important;
  opacity: 1 !important;
}

.swiper-pagination-bullet-active {
  width: 40px !important;
  height: 6px !important;
  border-radius: 3px !important;
  background: #277ef0 !important;
}

.advantage-center {
  width: 100%;
  height: 576px;
  background: #fff;
  box-sizing: border-box;
  padding: 60px 0 80px 0;
}

.header-padding {
  width: 100%;
}

.title-center {
  width: 100%;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: Kozuka Gothic Pr6N;
  font-weight: normal;
  font-size: 36px;
  color: #101419;
}

.title-center-l {
  background: #cccccc;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.title-center-swiper {
  width: 100%;
  display: flex;
  box-sizing: border-box;
  padding: 59px 16% 0 16%;
  justify-content: center;
}

.title-center-swiper-right {
  width: 50%;
  height: 337px;
  border: 1px solid #cdcdcd;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

.title-center-swiper-left {
  width: 50%;
  height: 337px;
  /* background: #277EF0; */
}

.title-center-swiper-left-icon {
  width: 70px;
  height: 70px;
  background: #f3f6fd;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-center-swiper-left-icon-img {
  width: 38px;
  height: 44px;
}

.title-center-swiper-left-title {
  font-family: Alibaba PuHuiTi 3;
  font-weight: normal;
  font-size: 32px;
  color: #277ef0;
  margin: 33px 0 34px 0;
}

.title-center-swiper-left-title-f {
  font-family: Alibaba PuHuiTi 3;
  font-weight: normal;
  font-size: 18px;
  color: #666666;
  line-height: 32px;
}

.header-banner {
  width: 100%;
  min-height: 400px;
  background: url("../img/build/back.png");
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 42px 14% 20px 16%;
  display: flex;
  justify-content: space-between;
}

.header-banner-title-center {
}

.header-banner-title {
  font-family: Alibaba PuHuiTi 3;
  font-weight: normal;
  font-size: 42px;
  color: #101419;
  line-height: 48px;
  box-sizing: border-box;
}

.header-banner-title-f {
  font-family: Alibaba PuHuiTi 3;
  font-weight: normal;
  font-size: 18px;
  color: #222c4b;
  line-height: 28px;
  box-sizing: border-box;
  padding: 38px 0;
}

.header-banner-right-img {
  width: 468px;
}

.header-banner-button {
  width: 120px;
  height: 44px;
  background: #277ef0;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: Alibaba PuHuiTi 3;
  font-weight: normal;
  font-size: 16px;
  color: #ffffff;
  line-height: 66px;
  cursor: pointer;
}

.header-banner-right-icon {
  width: 14px;
  height: 14px;
  margin-right: 6px;
}
.bottom-center {
  width: 100%;
  height: 40px;
  background: #101419;
  font-weight: normal;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}
.fixed {
  width: 580px;
  height: 300px;
  position: fixed;
  bottom: 100px;
  right: 10px;
  z-index: 999;
  background: #fff;
}
@media (max-width: 1366px) {
  .header-container {
    width: 94%;
  }
}

#myform input,
#myform1 input {
  padding-left: 10px;
}

/* 移动端模态框 - 默认在PC端隐藏 */
.mobile-modal {
  display: none;
}

/* PC端强制隐藏移动端模态框 */
@media (min-width: 769px) {
  .mobile-modal {
    display: none !important;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  /* Header Banner 区域适配 */
  .header-banner {
    flex-direction: column;
    padding: 30px 15px 20px 15px;
    height: auto;
    text-align: center;
    min-height: 0px;
  }

  .header-banner-title-center {
    width: 100%;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .header-banner-right-img {
    display: none !important;
  }

  .header-banner-title {
    text-align: center;
    font-size: 22px;
    line-height: 36px;
    font-weight: 600;
  }

  .header-banner-title-f {
    text-align: left;
    font-size: 16px;
    line-height: 24px;
    padding: 20px 0;
  }

  /* 车辆管理系统图片移动端适配 */
  .pro-img-9 img {
    width: 100% !important;
    height: auto;
  }

  /* pro-detail-item移动端样式 */
  .pro-detail-item {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .pro-detail-item li {
    display: flex;
    align-items: center;
    width: calc(50% - 5px);
    padding: 10px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    background-color: #fafafa;
  }

  .pro-detail-item img {
    margin-right: 8px;
    flex-shrink: 0;
    width: 24px;
    height: 24px;
  }

  .pro-detail-item span {
    font-size: 14px;
    flex: 1;
  }

  /* pro-name下的seal-point-box移动端样式 */
  .pro-name {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .pro-name .seal-point-box {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
    margin-bottom: 0;
    min-height: auto;
    padding: 15px;
  }

  .pro-name .seal-point-img {
    width: 60px;
    height: 60px;
  }

  .pro-name .seal-point-name {
    font-size: 16px;
    line-height: 24px;
    padding-left: 10px;
  }

  .pro-name .seal-point-content {
    padding-left: 20px;
    font-size: 12px;
    line-height: 20px;
  }

  .pro-name .seal-point-content::before {
    display: none !important;
    left: 10px;
    top: 8px;
  }

  .header-banner-button {
    margin: 0 auto;
  }

  /* 移动端按钮样式 - 参考PC端样式 */
  .header-banner-button2 {
    width: 120px;
    height: 44px;
    background: #277ef0;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: Alibaba PuHuiTi 3;
    font-weight: normal;
    font-size: 16px;
    color: #ffffff;
    cursor: pointer;
    margin: 0 auto;
  }

  .header-banner-button2 .header-banner-right-icon {
    width: 14px;
    height: 14px;
    margin-right: 6px;
  }

  /* 标题区域移动端适配 */
  .title-center-litle {
    font-size: 24px;
    font-weight: 600;
    padding: 0px 10px;
  }

  /* Swiper滑块内容居中显示 */
  .swiper-slide {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
  }

  /* 业务场景区域移动端适配 */
  .business-center-msg {
    flex-direction: column !important;
    align-items: center;
  }

  .business-center-msg-left {
    width: 100% !important;
    order: 1;
    margin-bottom: 20px;
    text-align: center;
    padding: 15px;
  }

  .business-center-msg-right {
    width: 100% !important;
    order: 2;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: auto !important;
    padding-top: 0px !important;
  }

  /* 价值优势部分适配 */
  .title-center-swiper {
    flex-direction: column;
    padding: 30px 15px 0 15px;
  }

  .title-center-swiper-left {
    width: 100% !important;
    order: 2;
    margin-top: 20px;
  }

  .title-center-swiper-right {
    width: 100% !important;
    order: 1;
    height: 250px;
  }

  .advantage-center {
    height: auto;
    padding: 40px 0 0px 0;
  }
  .business-center {
    padding-left: 0px;
    padding-right: 0px;
    height: auto;
  }
  .business-center-msg-layout {
    padding-left: 0px !important;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
  .business-center-bottom {
    height: auto !important;
  }

  /* 移动端模态框样式 */
  .mobile-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
  }

  .mobile-modal-content {
    position: relative;
    background-color: #fff;
    margin: 10% auto;
    padding: 0;
    width: 90%;
    max-width: 400px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }

  .mobile-modal-header {
    padding: 0.15rem;
    text-align: center;
    border-bottom: 1px solid #f5f6f9;
    position: relative;
  }

  .mobile-modal-close {
    position: absolute;
    right: 0.14rem;
    top: 0.14rem;
    width: 0.12rem;
    height: 0.12rem;
    cursor: pointer;
  }

  .mobile-modal-body {
    padding: 0.2rem 0.3rem;
  }

  .mobile-modal-footer {
    padding: 0.2rem;
    text-align: center;
    background: url("<?= get_stylesheet_directory_uri() ?>/assets/img/build/code-bac.png") no-repeat center;
    background-size: cover;
    border-radius: 0 0 8px 8px;
  }

  /* seal-solve-box移动端居中对齐 */
  .seal-solve-box {
    text-align: center;
  }

  /* seal-blank移动端布局调整 */
  .seal-blank {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .seal-blank img {
    margin-bottom: 20px;
  }

  /* seal-role移动端样式调整 */
  .seal-role {
    position: static;
    margin-bottom: 20px;
    max-width: 100%;
    text-align: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .seal-role1,
  .seal-role2,
  .seal-role3 {
    position: static;
    right: auto;
    left: auto;
    top: auto;
    bottom: auto;
    max-width: 100%;
    text-align: center;
    width: 100%;
  }

  .seal-role:last-child {
    margin-bottom: 0;
  }

  .role-name {
    font-size: 18px;
    margin-bottom: 8px;
  }

  .role-content {
    font-size: 14px;
    line-height: 1.5;
  }

  /* pro-intro和pro-line移动端样式调整 */
  .pro-intro {
    font-size: 18px;
    padding-top: 15px;
    padding-bottom: 8px;
    line-height: 1.4;
    text-align: center;
  }

  .pro-line {
    width: 30px;
    height: 3px;
    margin: 0 auto 15px;
  }

  .seal-value-box {
    width: 100% !important;
    float: none;
    margin-left: 0;
    margin-right: 0;
    margin-bottom: 15px;
    display: block;
  }

  .pro-value-img {
    height: 35px;
    margin-top: 20px;
  }

  .pro-value-content {
    padding: 8px 15px;
    margin-top: 15px;
    height: auto;
    min-height: 50px;
    font-size: 14px;
    line-height: 24px;
  }

  /* col-xs-12 mb-only容器移动端样式调整 */
  .col-xs-12.mb-only {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .col-xs-12.mb-only .col-xs-6 {
    width: 100% !important;
    margin-bottom: 15px;
  }

  .col-xs-12.mb-only .tiaozhan-slide-item {
    margin: 0 auto;
    max-width: 280px;
  }

  .col-xs-12.mb-only .tiaozhan-slide-item img {
    width: 100%;
    height: auto;
  }

  .col-xs-12.mb-only .tiaozhan-slide-item .tedian {
    width: 180px;
    height: 35px;
    line-height: 35px;
    font-size: 14px;
    bottom: -10px;
  }

  /* chanpin-youshi-item 移动端样式调整 */
  .mb-only .chanpin-youshi-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background: #f9f9f9;
    border-radius: 8px;
  }

  .mb-only .chanpin-youshi-item img {
    width: 40px;
    height: 36px;
    margin-right: 15px;
    flex-shrink: 0;
  }

  .mb-only .chanpin-youshi-item span {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    line-height: 1.4;
    flex: 1;
  }

  .metting-manage-item img {
    width: 100%;
    max-width: 300px;
    height: auto;
    float: none;
    display: block;
    margin: 0 auto 20px auto;
  }

  .metting-manage-item .text {
    padding: 20px;
    padding-left: 20px;
  }

  .chanpin-jiazhi .text-container {
    position: static;
    width: 100%;
    height: auto;
    opacity: 0.5;
    top: auto;
    right: auto;
    padding: 20px;
  }

  /* youshi-container 移动端样式调整 */
  .mb-only .youshi-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
  }

  .mb-only .youshi-container .youshi-item {
    width: 100%;
    max-width: 300px;
    text-align: center;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .mb-only .youshi-container .youshi-item img {
    width: 80px;
    height: auto;
  }

  .mb-only .youshi-container .youshi-item .bt-line {
    width: 30px;
    height: 3px;
    background: #ffbc4d;
    margin: 15px auto;
  }

  .mb-only .youshi-container .youshi-item .number {
    font-size: 20px;
    font-weight: bold;
    color: #333333;
    margin: 10px 0;
  }

  .mb-only .youshi-container .youshi-item .text {
    font-size: 14px;
    line-height: 1.6;
    width: 100%;
    margin: 15px 0 0 0;
    color: #666666;
  }

  /* tjfx-title 移动端样式 */
  .tjfx-title {
    text-align: center;
    font-size: 18px;
    font-weight: 600;
  }

  /* tjfx-text 移动端样式 */
  .tjfx-text {
    margin: 15px 0;
  }

  /* single-orgmanage.php 中 mb-only 容器内的 col-xs-6 和 youshi-item2 移动端样式 */
  .mb-only .col-xs-6 {
    width: 100%;
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
  }

  .mb-only .youshi-item2 {
    width: 100%;
    max-width: 300px;
    background: #fff;
    padding: 20px;
    text-align: center;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin: 0 auto;
  }

  .mb-only .youshi-item2 img {
    width: 30px;
    height: auto;
    margin-bottom: 10px;
  }

  .mb-only .youshi-item2 .role {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
  }

  .mb-only .youshi-item2 .text {
    width: 100%;
    font-size: 12px;
    color: #666;
    line-height: 1.5;
    margin: 8px 0;
  }

  .mb-only .youshi-item2 .number {
    font-size: 12px;
    color: #999;
    margin-top: 10px;
  }
}
