/* 企业微信生态解决方案页面样式 */

/* Font Awesome图标确保正确显示 */
.fas,
.far,
.fab,
.fa {
  font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands" !important;
  font-weight: 900;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 全局美化设置 */
html {
  scroll-behavior: smooth;
}

body {
  font-family: "Noto Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  line-height: 1.6;
  color: #2d3748;
  background: #ffffff;
}

/* 选择文本的颜色 */
::selection {
  background: rgba(102, 126, 234, 0.2);
  color: #2d3748;
}

::-moz-selection {
  background: rgba(102, 126, 234, 0.2);
  color: #2d3748;
}

/* CSS变量定义 */
:root {
  --primary-color: #2563eb;
  --secondary-color: #1e40af;
  --text-color: #1f2937;
  --light-bg: #f3f4f6;
  --white: #ffffff;
}

/* 通用标题样式 */
.section-header {
  text-align: center;
  margin-bottom: 80px;
  position: relative;
}

.section-header h2 {
  padding-top: 100px;
  font-size: 32px;
  color: var(--text-color);
  font-weight: 600;
  display: inline-block;
  position: relative;
  padding-bottom: 15px;
  margin-bottom: 0;
}

.section-header h2::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  border-radius: 2px;
}

.section-header p {
  font-size: 16px;
  color: #666;
  margin-top: 20px;
  margin-bottom: 0;
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* 通用按钮样式 */
.cta-button,
.primary-button,
.secondary-button {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s;
}

.primary-button {
  background: var(--primary-color);
  color: var(--white);
}

.secondary-button {
  background: var(--white);
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  margin-left: 1rem;
}

.cta-button {
  background: var(--primary-color);
  color: var(--white);
  white-space: nowrap;
  margin-left: 5px;
}

.primary-button:hover,
.cta-button:hover {
  background: var(--secondary-color);
  transform: translateY(-2px);
}

.secondary-button:hover {
  background: var(--light-bg);
  transform: translateY(-2px);
}

/* 企业微信解决方案专用容器样式 - 与header保持一致 */
.wecom-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Banner样式 - 增强视觉效果 */
#hero {
  width: 100%;
  position: relative;
  overflow: hidden;
}

.banner-container {
  width: 100%;
  height: 600px;
  position: relative;
  overflow: hidden;
}

.banner-item {
  width: 100%;
  height: 100%;
  position: relative;
}

.banner-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transition: transform 0.5s ease;
}

.banner-item:hover img {
  transform: scale(1.05);
}

/* 解决方案介绍部分 - 简洁白色背景 */
.solution-intro-section {
  padding: 120px 0;
  background: #ffffff;
  position: relative;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.solution-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  position: relative;
  z-index: 2;
}

.solution-text {
  max-width: 600px;
  text-align: left;
  animation: slideInLeft 1s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.solution-title {
  font-size: 32px;
  color: #2d3748;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.2;
}

.solution-subtitle {
  font-size: 20px;
  color: #4a5568;
  margin-bottom: 28px;
  font-weight: 500;
}

.solution-desc {
  font-size: 16px;
  color: #718096;
  line-height: 1.8;
  margin-bottom: 40px;
  text-align: justify;
}

/* 特性列表美化 */
.solution-features {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  margin-top: 20px;
}

.feature-text {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #ffffff;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  color: #2d3748;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.feature-text:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  width: 6px;
  height: 6px;
  background: #3182ce;
  border-radius: 50%;
  margin-right: 10px;
  flex-shrink: 0;
}

/* 图片区域美化 */
.solution-image {
  width: 100%;
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: slideInRight 1s ease-out;
  position: relative;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.solution-image img {
  width: 100%;
  height: auto;
  max-width: 500px;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.solution-image:hover img {
  transform: scale(1.02) rotate(1deg);
}

/* 企业微信应用开发部分 */
.wecom-dev-section {
  background: #f8f9fa;
  position: relative;
}

.dev-content {
  margin-top: 60px;
}

.dev-block {
  background: #ffffff;
  border-radius: 12px;
  padding: 50px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.dev-block:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.dev-block h3 {
  font-size: 24px;
  color: #2d3748;
  margin-bottom: 20px;
  font-weight: 600;
}

.dev-intro {
  font-size: 16px;
  color: #4a5568;
  line-height: 1.8;
  margin-bottom: 40px;
}

.dev-items {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
}

.dev-item {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 8px;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
  margin-bottom: 20px;
}

.dev-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  background: #ffffff;
}

.dev-item h4 {
  font-size: 20px;
  color: #2d3748;
  margin-bottom: 16px;
  font-weight: 600;
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
}

.dev-item h4 i {
  color: #3182ce;
  margin-right: 10px;
  font-size: 18px;
  display: inline-block;
  width: 20px;
  text-align: center;
}

/* 如果Font Awesome未加载，显示备用符号 */
.dev-item h4 i:before {
  content: "●";
  font-family: Arial, sans-serif;
}

.dev-item h4 i.fa-cogs:before {
  content: "⚙";
}
.dev-item h4 i.fa-users:before {
  content: "👥";
}
.dev-item h4 i.fa-robot:before {
  content: "🤖";
}
.dev-item h4 i.fa-chart-bar:before {
  content: "📊";
}
.dev-item h4 i.fa-plug:before {
  content: "🔌";
}
.dev-item h4 i.fa-code:before {
  content: "💻";
}
.dev-item h4 i.fa-store:before {
  content: "🏪";
}

.dev-item p {
  font-size: 15px;
  color: #4a5568;
  line-height: 1.7;
  margin: 0;
  position: relative;
  z-index: 2;
}

/* 私有化企业微信运维部分 */
.maintenance-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;
  margin-top: 40px;
}

.maintenance-card {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.card-image {
  width: 100%;
  height: 240px;
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.maintenance-card:hover .card-image img {
  transform: scale(1.05);
}

.card-content {
  padding: 30px;
}

.card-content h3 {
  font-size: 24px;
  color: #000;
  margin-bottom: 20px;
  font-weight: 500;
}

.card-content ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.card-content li {
  font-size: 15px;
  color: #000;
  line-height: 1.6;
  margin-bottom: 16px;
  padding-left: 24px;
  position: relative;
}

.card-content li:before {
  content: "";
  position: absolute;
  left: 0;
  top: 8px;
  width: 6px;
  height: 6px;
  background-color: #4a90e2;
  border-radius: 50%;
}

/* 腾讯会议应用开发部分 */
.meeting-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;
  margin-top: 40px;
}

.meeting-card {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.meeting-card:hover {
  transform: translateY(-5px);
}

.meeting-card .card-image {
  width: 100%;
  height: 240px;
  overflow: hidden;
}

.meeting-card .card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.meeting-card:hover .card-image img {
  transform: scale(1.05);
}

.meeting-card .card-content {
  padding: 30px;
  background: #fff;
}

.meeting-card .card-content h3 {
  font-size: 24px;
  color: #000;
  margin-bottom: 20px;
  font-weight: 500;
}

.meeting-card .card-content ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.meeting-card .card-content li {
  font-size: 15px;
  color: #000;
  line-height: 1.6;
  margin-bottom: 16px;
  padding-left: 24px;
  position: relative;
}

.meeting-card .card-content li:before {
  content: "";
  position: absolute;
  left: 0;
  top: 8px;
  width: 6px;
  height: 6px;
  background-color: #4a90e2;
  border-radius: 50%;
}

/* 腾讯乐享定制部分 */
.lexiang-section {
  background: #f8f9fa;
}

.lexiang-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-top: 40px;
}

.lexiang-card {
  background: #fff;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
  margin-bottom: 30px;
}

.lexiang-card:hover {
  transform: translateY(-5px);
}

.lexiang-card h3 {
  font-size: 20px;
  color: #000;
  margin-bottom: 20px;
  font-weight: 500;
}

.lexiang-card h3 i {
  color: #1890ff;
  margin-right: 10px;
  font-size: 18px;
  display: inline-block;
  width: 20px;
  text-align: center;
}

/* 腾讯乐享图标备用符号 */
.lexiang-card h3 i:before {
  content: "●";
  font-family: Arial, sans-serif;
}

.lexiang-card h3 i.fa-database:before {
  content: "🗄";
}
.lexiang-card h3 i.fa-chart-line:before {
  content: "📈";
}
.lexiang-card h3 i.fa-route:before {
  content: "🛤";
}
.lexiang-card h3 i.fa-comments:before {
  content: "💬";
}
.lexiang-card h3 i.fa-mobile-screen:before {
  content: "📱";
}
.lexiang-card h3 i.fa-graduation-cap:before {
  content: "🎓";
}

.lexiang-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.lexiang-card li {
  font-size: 15px;
  color: #000;
  line-height: 1.6;
  margin-bottom: 16px;
  padding-left: 24px;
  position: relative;
}

.lexiang-card li:before {
  content: "";
  position: absolute;
  left: 0;
  top: 8px;
  width: 6px;
  height: 6px;
  background-color: #4a90e2;
  border-radius: 50%;
}

/* 企微应用模块样式 */
.wecom-apps-section {
  background: #fff;
}

.apps-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
  margin-top: 40px;
  margin-bottom: 40px;
}

.app-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  max-width: 600px;
  margin: 0 auto;
}

.app-card-top {
  width: 100%;
  height: 240px;
  overflow: hidden;
}

.app-card-top img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.app-card:hover .app-card-top img {
  transform: scale(1.05);
}

.app-card-bottom {
  padding: 25px;
}

.app-card-content {
  display: grid;
  grid-template-columns: 1.2fr 0.8fr;
  gap: 20px;
  align-items: start;
}

.app-info h3 {
  font-size: 24px;
  color: #333;
  margin-bottom: 20px;
  margin-top: 30px;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.app-info h3:first-child {
  margin-top: 0;
}

.app-info h3 i {
  color: #1890ff;
  margin-right: 10px;
  font-size: 22px;
  display: inline-block;
  width: 26px;
  text-align: center;
}

/* 应用展示图标备用符号 */
.app-info h3 i:before {
  content: "●";
  font-family: Arial, sans-serif;
}

.app-info h3 i.fa-balance-scale:before {
  content: "⚖";
}
.app-info h3 i.fa-money-check:before {
  content: "💰";
}

.app-info ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.app-info li {
  font-size: 15px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 12px;
  padding-left: 20px;
  position: relative;
}

.app-info li:before {
  content: "";
  position: absolute;
  left: 0;
  top: 8px;
  width: 6px;
  height: 6px;
  background-color: #1890ff;
  border-radius: 50%;
}

.app-image {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.app-image img {
  width: 100%;
  height: auto;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 10px;
  background: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* 响应式调整 */
@media (max-width: 992px) {
  /* 容器平板样式 - 与header保持一致 */
  .wecom-container {
    padding: 0 15px;
  }

  .solution-grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .solution-text {
    max-width: 100%;
  }

  .dev-items {
    grid-template-columns: 1fr;
  }

  .maintenance-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .meeting-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .app-card-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .app-image {
    order: -1;
  }
}

@media (max-width: 768px) {
  /* 容器响应式样式 - 与header保持一致 */
  .wecom-container {
    padding: 0 15px;
  }

  /* 标题响应式样式 */
  .section-header {
    margin-bottom: 50px;
  }

  .section-header h2 {
    font-size: 28px;
  }

  /* Banner移动端优化 - 隐藏banner */
  #hero {
    display: none;
  }

  /* 解决方案介绍移动端优化 */
  .solution-intro-section {
    padding: 80px 0;
  }

  /* 移动端section间距调整 */
  .wecom-dev-section,
  .lexiang-section,
  .wecom-apps-section {
    padding: 0px !important;
  }

  .solution-grid {
    gap: 40px;
  }

  .solution-title {
    font-size: 28px;
    text-align: center;
  }

  .solution-subtitle {
    font-size: 18px;
    text-align: center;
  }

  .solution-features {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  /* 开发部分移动端优化 */
  .wecom-dev-section {
    padding: 80px 0;
  }

  .dev-block {
    padding: 30px 20px;
    margin-bottom: 30px;
  }

  .dev-block h3 {
    font-size: 24px;
    text-align: center;
  }

  .dev-items {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .dev-item {
    padding: 24px;
  }

  .dev-item h4 {
    font-size: 18px;
  }

  .solution-intro-section,
  .wecom-dev-section {
    padding: 60px 0;
  }

  .solution-title {
    font-size: 28px;
    margin-bottom: 12px;
  }

  .solution-subtitle {
    font-size: 16px;
    margin-bottom: 20px;
  }

  .solution-desc {
    font-size: 15px;
    margin-bottom: 30px;
  }

  .solution-features {
    gap: 16px;
  }

  .feature-text {
    font-size: 16px;
  }

  .dev-block {
    padding: 20px;
  }

  .dev-block h3 {
    font-size: 20px;
  }

  .dev-item h4 {
    font-size: 16px;
  }

  .dev-item p {
    font-size: 14px;
  }

  .card-image {
    height: 200px;
  }

  .card-content {
    padding: 20px;
  }

  .card-content h3 {
    font-size: 20px;
    margin-bottom: 16px;
  }

  .card-content li {
    font-size: 14px;
    margin-bottom: 12px;
  }

  .meeting-card .card-image {
    height: 200px;
  }

  .meeting-card .card-content {
    padding: 20px;
  }

  .meeting-card .card-content h3 {
    font-size: 20px;
    margin-bottom: 16px;
  }

  .meeting-card .card-content li {
    font-size: 14px;
    margin-bottom: 12px;
  }

  .app-card-top {
    height: 200px;
  }

  .app-card-bottom {
    padding: 20px;
  }

  .app-info h3 {
    font-size: 20px;
    margin-bottom: 15px;
  }

  .app-info h3 i {
    font-size: 18px;
  }

  .app-info li {
    font-size: 14px;
  }
}

@media (max-width: 1200px) {
  .lexiang-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .lexiang-section {
    padding: 60px 0;
  }

  .lexiang-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .lexiang-card {
    padding: 20px;
  }

  .lexiang-card h3 {
    font-size: 18px;
    margin-bottom: 16px;
  }

  .lexiang-card li {
    font-size: 14px;
    margin-bottom: 12px;
  }
}

@media (max-width: 1200px) {
  .app-card-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .app-image {
    order: -1;
  }
}

@media (max-width: 992px) {
  .apps-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
}
