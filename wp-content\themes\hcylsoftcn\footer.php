<?php

/**
 * The template for displaying the footer
 *
 * Contains the closing of the #content div and all content after.
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package hcylsoftcn
 */

?>

<?php

get_template_part('template-parts/footer', 'common');

wp_footer();

?>
<script type="text/javascript">
    // banner--pc、pad、移动端
    var swiper1 = new Swiper('.swiper-container-banner', {
        loop: false,
        pagination: {
            el: '.swiper-pagination',
        },
        autoplay: {
            delay: 4000,
        },
    });
    // 产品中心--响应式
    var swiper2 = new Swiper('.swiper-container-product', {
        loop: false,
        navigation: {
            nextEl: '.swiper-button-next-product',
            prevEl: '.swiper-button-prev-product',
        },
        observer: true,
        observeParents: true,
        autoplay: {
            delay: 4000,
        },
        allowTouchMove: true,
        breakpoints: {
            // 移动端
            320: {
                slidesPerView: 1,
                spaceBetween: 10,
            },
            // 平板
            768: {
                slidesPerView: 3,
                spaceBetween: 20,
            },
            // PC端
            1024: {
                slidesPerView: 4,
                spaceBetween: 30,
                simulateTouch: false,
            }
        }
    });
    // 解决方案--响应式
    var swiper3 = new Swiper('.swiper-container-solution', {
        loop: false,
        navigation: {
            nextEl: '.swiper-button-next-solution',
            prevEl: '.swiper-button-prev-solution',
        },
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        },
        observer: true,
        observeParents: true,
        autoplay: {
            delay: 4000,
        },
        allowTouchMove: true,
        watchSlidesProgress: true,
        watchSlidesVisibility: true,
        breakpoints: {
            // 移动端
            320: {
                slidesPerView: 1,
                spaceBetween: 20,
                loop: true,
            },
            // 平板
            768: {
                slidesPerView: 2,
                spaceBetween: 30,
            },
            // PC端
            1024: {
                slidesPerView: 3,
                spaceBetween: 30,
                simulateTouch: false,
            }
        }
    });
    // 生态解决方案--响应式
    var swiper4 = new Swiper('.swiper-container-eco-solution', {
        loop: false,
        navigation: {
            nextEl: '.swiper-button-next-eco-solution',
            prevEl: '.swiper-button-prev-eco-solution',
        },
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        },
        observer: true,
        observeParents: true,
        autoplay: {
            delay: 4000,
        },
        allowTouchMove: true,
        watchSlidesProgress: true,
        watchSlidesVisibility: true,
        breakpoints: {
            // 移动端
            320: {
                slidesPerView: 1,
                spaceBetween: 20,
                loop: true,
            },
            // 平板
            768: {
                slidesPerView: 2,
                spaceBetween: 30,
            },
            // PC端
            1024: {
                slidesPerView: 3,
                spaceBetween: 30,
                simulateTouch: false,
            }
        }
    });
    // 合作伙伴--pad
    var swiper7 = new Swiper('.padswiper-container-partner', {
        loop: true,
        slidesPerView: 2,
        spaceBetween: 30,
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
        simulateTouch: false,
    });
</script>
</body>

</html>