<?php

/**
 * The main template file
 *
 * This is the most generic template file in a WordPress theme
 * and one of the two required files for a theme (the other being style.css).
 * It is used to display a page when nothing more specific matches a query.
 * E.g., it puts together the home page when no home.php file exists.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package hcylsoftcn
 */

get_header();
?>

<!-- Swiper -->
<div class="swiper-container-banner">
    <div class="swiper-wrapper">
        <?php
        $banners = banners();
        foreach ($banners as $banner):
            if (get_theme_mod($banner['id']) != ''):
        ?>
                <div class="swiper-slide">
                    <img id="<?php echo $banner['id']; ?>" src="<?php echo get_theme_mod($banner['id']); ?>" alt="" class="banner-img" />
                </div>
        <?php
            endif;
        endforeach;
        ?>
    </div>
</div>
<!-- 公司简介开始 -->
<div class="profile profile-company">
    <p class="profile-title profile-title-e">COMPANY PROFILE</p>
    <p class="profile-title profile-title-c">公司简介</p>
    <p class="profile-line"></p>
    <div class="company-profile">
        <div class="company-profile-l">
            <p class="company-profile-title">华创云联<span
                    style="display:inline-block;margin-top:5px;margin-left:6px;">About Us</span></p>
            <p class="company-profile-content">
                <?php
                echo get_theme_mod('companyprofile');
                ?>
            </p>
            <a href="<?php echo get_permalink('199'); ?>" class="more" target="_self">查看更多</a>
        </div>
        <div class="company-profile-r">
            <img src="<?php echo get_theme_mod('companyprofile_img'); ?>" alt="" class="company-img" />
        </div>
    </div>
</div><!-- 公司简介结束 -->
<!-- 解决方案开始 -->
<div class="profile profile-solution">
    <p class="profile-title profile-title-e">THE SOLUTION</p>
    <p class="profile-title profile-title-c">解决方案</p>
    <p class="profile-line"></p>
    <?php

    query_posts('cat=7');

    // 收集所有解决方案数据
    $solution_posts = array();
    while (have_posts()):
        the_post();
        $solution_posts[] = array(
            'permalink' => esc_url(get_permalink()),
            'thumbnail' => get_the_post_thumbnail(),
            'title' => get_the_title(),
            'content' => limitContent(250),
            'excerpt' => get_the_excerpt()
        );
    endwhile;
    wp_reset_query();

    ?>
    <!-- 解决方案轮播 -->
    <div class="company-profile-box">
        <div class="hcyl-solution" style="display: flex;align-items: center;">
            <div class="swiper-button-prev-solution"></div>
            <!-- Unified Responsive Swiper -->
            <div class="swiper-container-solution">
                <div class="swiper-wrapper">
                    <?php
                    foreach ($solution_posts as $index => $post):
                    ?>
                        <div class="swiper-slide swiper-slide-solution">
                            <div class="solution-item">
                                <div class="solution-img">
                                    <a href="<?php echo $post['permalink']; ?>">
                                        <?php echo $post['thumbnail']; ?>
                                    </a>
                                </div>
                                <div class="solution-content-box">
                                    <a href="<?php echo $post['permalink']; ?>" style="text-decoration: none;">
                                        <p class="solution-title"><?php echo $post['title']; ?></p>
                                    </a>
                                    <p class="solution-content">
                                        <?php echo $post['content']; ?>
                                    </p>
                                    <a href="<?php echo $post['permalink']; ?>" class="solution-get-btn">
                                        获取解决方案
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php
                    endforeach;
                    ?>
                </div>
            </div>
            <div class="swiper-button-next-solution"></div>
        </div>
    </div>

</div><!-- 解决方案结束 -->
<!-- 生态解决方案开始 -->
<div class="profile profile-eco-solution">
    <p class="profile-title profile-title-e">ECO SOLUTION</p>
    <p class="profile-title profile-title-c">生态解决方案</p>
    <p class="profile-line"></p>
    <?php
    // 先查找 alias = 'ecosphere' 的父页面
    $parent_page = get_posts(array(
        'post_type' => 'page',
        'meta_query' => array(
            array(
                'key' => 'alias',
                'value' => 'ecosphere',
                'compare' => '='
            )
        ),
        'posts_per_page' => 1
    ));

    $eco_solution_posts = array();

    if (!empty($parent_page)) {
        $parent_id = $parent_page[0]->ID;

        // 查询该父页面的所有子页面
        $child_pages = get_posts(array(
            'post_type' => 'page',
            'post_parent' => $parent_id,
            'posts_per_page' => -1,
            'post_status' => 'publish',
            'orderby' => 'menu_order',
            'order' => 'ASC'
        ));

        // 收集所有生态解决方案数据
        foreach ($child_pages as $post) {
            setup_postdata($post);

            // 直接从页面内容中截取摘要
            $content = $post->post_content;

            // 移除HTML标签和shortcode
            $content = strip_tags($content);
            $content = strip_shortcodes($content);

            // 截取前80个词作为摘要，确保至少3行显示
            $excerpt = wp_trim_words($content, 80, '...');

            $eco_solution_posts[] = array(
                'permalink' => esc_url(get_permalink($post->ID)),
                'thumbnail' => get_the_post_thumbnail($post->ID),
                'title' => get_the_title($post->ID),
                'content' => $excerpt,
                'excerpt' => $excerpt
            );
        }
        wp_reset_postdata();
    }

    ?>
    <!-- 生态解决方案轮播 -->
    <div class="company-profile-box">
        <div class="hcyl-eco-solution" style="display: flex;align-items: center;">
            <div class="swiper-button-prev-eco-solution"></div>
            <!-- Unified Responsive Swiper -->
            <div class="swiper-container-eco-solution">
                <div class="swiper-wrapper">
                    <?php
                    foreach ($eco_solution_posts as $index => $post):
                    ?>
                        <div class="swiper-slide swiper-slide-eco-solution">
                            <div class="solution-item">
                                <div class="solution-img">
                                    <a href="<?php echo $post['permalink']; ?>">
                                        <?php echo $post['thumbnail']; ?>
                                    </a>
                                </div>
                                <div class="solution-content-box">
                                    <a href="<?php echo $post['permalink']; ?>" style="text-decoration: none;">
                                        <p class="solution-title"><?php echo $post['title']; ?></p>
                                    </a>
                                    <p class="solution-content">
                                        <?php echo $post['content']; ?>
                                    </p>
                                    <a href="<?php echo $post['permalink']; ?>" class="solution-get-btn">
                                        获取解决方案
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php
                    endforeach;
                    ?>
                </div>
            </div>
            <div class="swiper-button-next-eco-solution"></div>
        </div>
    </div>

</div><!-- 生态解决方案结束 -->
<!-- 产品中心开始 -->
<div class="profile profile-product">
    <p class="profile-title profile-title-e">COMPANY PROFILE</p>
    <p class="profile-title profile-title-c" style="color:#fff;">产品中心</p>
    <p class="profile-line"></p>
    <?php
    query_posts("cat=8");

    ?>
    <!-- 产品轮播 -->
    <div class="company-profile-box">
        <div style="display: flex;justify-content: center;align-items: center;">
            <div class="swiper-button-prev-product"></div>
            <!-- Unified Responsive Swiper -->
            <div class="swiper-container-product">

                <div class="swiper-wrapper">
                    <?php
                    while (have_posts()):
                        the_post();
                    ?>
                        <div class="swiper-slide swiper-slide-product">
                            <a href="<?= get_permalink(); ?>" class="product-item">
                                <div class="product-img">
                                    <?php the_post_thumbnail(); ?>
                                </div>
                                <p class="product-title" style="font-size: 16px"><?= the_title(); ?></p>
                                <p class="product-content">
                                    <?php
                                    echo limitContent(80);
                                    ?>
                                </p>
                            </a>
                        </div>
                    <?php
                    endwhile;
                    ?>
                </div>
            </div>
            <div class="swiper-button-next-product"></div>
        </div>
    </div>

</div><!-- 产品中心结束 -->
<!-- 合作伙伴开始 -->
<div class="profile profile-partner">
    <p class="profile-title profile-title-e">COOPERATION PARTNERS</p>
    <p class="profile-title profile-title-c">合作伙伴</p>
    <p class="profile-line"></p>
    <div class="partner-container">
        <?php
        query_posts(array("p" => 164));
        while (have_posts()):
            the_post();
            the_content();
        endwhile;
        wp_reset_query();
        ?>
    </div>

</div><!-- 合作伙伴结束 -->

<?php
get_footer();
